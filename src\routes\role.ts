import { FastifyInstance } from 'fastify';
import { RoleController } from '../controllers/roleController.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission } from '../middleware/permission.js';
import { DatabaseService } from '../services/database.js';
import { RoleService } from '../services/roleService.js';

export async function roleRoutes(fastify: FastifyInstance) {
  // 初始化服务和控制器
  const databaseService = new DatabaseService();
  const roleService = new RoleService(databaseService);
  const roleController = new RoleController(roleService);

  // 创建角色
  fastify.post('/roles', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_CREATE)],
    schema: {
      description: '创建角色',
      tags: ['Role'],
      body: {
        type: 'object',
        required: ['name', 'displayName'],
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 100, description: '角色名称' },
          displayName: { type: 'string', minLength: 1, maxLength: 100, description: '角色显示名称' },
          description: { type: 'string', description: '角色描述' },
          isSystem: { type: 'boolean', description: '是否为系统角色' },
          isActive: { type: 'boolean', description: '是否激活' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                displayName: { type: 'string' },
                description: { type: 'string' },
                isSystem: { type: 'boolean' },
                isActive: { type: 'boolean' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
                createdBy: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.createRole.bind(roleController));

  // 更新角色
  fastify.put('/roles/:roleId', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_UPDATE)],
    schema: {
      description: '更新角色',
      tags: ['Role'],
      params: {
        type: 'object',
        required: ['roleId'],
        properties: {
          roleId: { type: 'string', description: '角色ID' },
        },
      },
      body: {
        type: 'object',
        properties: {
          displayName: { type: 'string', minLength: 1, maxLength: 100, description: '角色显示名称' },
          description: { type: 'string', description: '角色描述' },
          isActive: { type: 'boolean', description: '是否激活' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                displayName: { type: 'string' },
                description: { type: 'string' },
                isSystem: { type: 'boolean' },
                isActive: { type: 'boolean' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
                createdBy: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.updateRole.bind(roleController));

  // 删除角色
  fastify.delete('/roles/:roleId', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_DELETE)],
    schema: {
      description: '删除角色',
      tags: ['Role'],
      params: {
        type: 'object',
        required: ['roleId'],
        properties: {
          roleId: { type: 'string', description: '角色ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.deleteRole.bind(roleController));

  // 获取角色详情
  fastify.get('/roles/:roleId', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_READ)],
    schema: {
      description: '获取角色详情',
      tags: ['Role'],
      params: {
        type: 'object',
        required: ['roleId'],
        properties: {
          roleId: { type: 'string', description: '角色ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                displayName: { type: 'string' },
                description: { type: 'string' },
                isSystem: { type: 'boolean' },
                isActive: { type: 'boolean' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
                createdBy: { type: 'string' },
                rolePermissions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      permission: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          name: { type: 'string' },
                          displayName: { type: 'string' },
                          description: { type: 'string' },
                          module: { type: 'string' },
                          action: { type: 'string' },
                          resource: { type: 'string' },
                        },
                      },
                    },
                  },
                },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.getRole.bind(roleController));

  // 获取角色列表
  fastify.get('/roles', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_READ)],
    schema: {
      description: '获取角色列表',
      tags: ['Role'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          isActive: { type: 'string', description: '是否激活' },
          isSystem: { type: 'string', description: '是否为系统角色' },
          search: { type: 'string', description: '搜索关键词' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                roles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                      description: { type: 'string' },
                      isSystem: { type: 'boolean' },
                      isActive: { type: 'boolean' },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' },
                      createdBy: { type: 'string' },
                    },
                  },
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.getRoles.bind(roleController));

  // 为角色分配权限
  fastify.post('/roles/:roleId/permissions', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_ASSIGN)],
    schema: {
      description: '为角色分配权限',
      tags: ['Role'],
      params: {
        type: 'object',
        required: ['roleId'],
        properties: {
          roleId: { type: 'string', description: '角色ID' },
        },
      },
      body: {
        type: 'object',
        required: ['permissionIds'],
        properties: {
          permissionIds: {
            type: 'array',
            items: { type: 'string' },
            description: '权限ID列表',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.assignPermissions.bind(roleController));

  // 获取角色的权限列表
  fastify.get('/roles/:roleId/permissions', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_READ)],
    schema: {
      description: '获取角色的权限列表',
      tags: ['Role'],
      params: {
        type: 'object',
        required: ['roleId'],
        properties: {
          roleId: { type: 'string', description: '角色ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                permissions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                      description: { type: 'string' },
                      module: { type: 'string' },
                      action: { type: 'string' },
                      resource: { type: 'string' },
                    },
                  },
                },
                total: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.getRolePermissions.bind(roleController));

  // 检查角色名称是否可用
  fastify.get('/roles/check-name', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_READ)],
    schema: {
      description: '检查角色名称是否可用',
      tags: ['Role'],
      querystring: {
        type: 'object',
        required: ['name'],
        properties: {
          name: { type: 'string', description: '角色名称' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                available: { type: 'boolean' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.checkRoleName.bind(roleController));

  // 检查指定角色名称是否可用（更新时使用）
  fastify.get('/roles/:roleId/check-name', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_READ)],
    schema: {
      description: '检查指定角色名称是否可用（更新时使用）',
      tags: ['Role'],
      params: {
        type: 'object',
        required: ['roleId'],
        properties: {
          roleId: { type: 'string', description: '角色ID' },
        },
      },
      querystring: {
        type: 'object',
        required: ['name'],
        properties: {
          name: { type: 'string', description: '角色名称' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                available: { type: 'boolean' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, roleController.checkRoleName.bind(roleController));
}
