import cors from "@fastify/cors";
import multipart from "@fastify/multipart";
import fastifyStatic from "@fastify/static";
import Fastify from "fastify";
import { dirname, join } from "path";
import { fileURLToPath } from "url";
import { env } from "./config/env.js";
import { appRoutes } from "./routes/app.js";
import { authRoutes } from "./routes/auth.js";

// 设置时区
process.env.TZ = env.TIMEZONE;
console.log(`🌍 时区设置为: ${env.TIMEZONE}`);

import { DingTalkStreamServiceV2 } from "./services/dingtalk-stream-v2-updated.js";
import { redisService } from "./services/redis.js";
console.log("[ redisService ] >", redisService);

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 创建Fastify实例
const fastify = Fastify({
  logger:
    env.NODE_ENV === "development"
      ? {
          level: env.LOG_LEVEL,
          transport: {
            target: "pino-pretty",
            options: {
              colorize: true,
              translateTime: "HH:MM:ss Z",
              ignore: "pid,hostname",
            },
          },
        }
      : {
          level: env.LOG_LEVEL,
          // 生产环境使用标准JSON格式日志
          serializers: {
            req: (req: any) => ({
              method: req.method,
              url: req.url,
              headers: req.headers,
            }),
            res: (res: any) => ({
              statusCode: res.statusCode,
            }),
          },
        },
});

// 注册CORS插件
await fastify.register(cors, {
  origin: true, // 允许所有来源，生产环境应该配置具体域名
  credentials: true,
});

// 注册文件上传插件
await fastify.register(multipart, {
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
});
// 注册静态文件服务 public/
await fastify.register(fastifyStatic, {
  root: [join(__dirname, "..", "public"), join(__dirname, "..", "uploads")],
  prefix: "/", // 可选: 默认为 '/'
});

//uploads/financial/
// await fastify.register(fastifyStatic, {
//   root: join(__dirname, '..', 'uploads'),
//   prefix: '/uploads/', // 可选: 默认为 '/'
// });

// 注册路由
await fastify.register(authRoutes, { prefix: "/api" });
await fastify.register(appRoutes, { prefix: "/api" });

// 导入项目管理路由
import { projectRoutes } from "./routes/project.js";
await fastify.register(projectRoutes, { prefix: "/api" });

// 导入收入管理路由
import { revenueRoutes } from "./routes/revenue.js";
await fastify.register(revenueRoutes, { prefix: "/api" });

// 导入供应商管理路由
import { supplierRoutes } from "./routes/supplier.js";
await fastify.register(supplierRoutes, { prefix: "/api" });

// 导入周预算管理路由
import { weeklyBudgetRoutes } from "./routes/weeklyBudget.js";
await fastify.register(weeklyBudgetRoutes, { prefix: "/api" });

// 导入审批管理路由
import { approvalRoutes } from "./routes/approval.js";
await fastify.register(approvalRoutes, { prefix: "/api" });

// 导入测试路由（仅开发环境）
import { testRoutes } from "./routes/test.js";
await fastify.register(testRoutes, { prefix: "/api" });

// 导入 Redis 管理路由
import { redisRoutes } from "./routes/redis.js";
await fastify.register(redisRoutes, { prefix: "/api" });

// 导入钉钉回调路由
import { dingTalkCallbackRoutes } from "./routes/dingtalk-callback.js";
await fastify.register(dingTalkCallbackRoutes, { prefix: "/api" });

// 导入钉钉Stream管理路由
import {
  dingTalkStreamRoutes,
  setGlobalStreamService,
} from "./routes/dingtalk-stream.js";
await fastify.register(dingTalkStreamRoutes, { prefix: "/api" });

// 导入财务报表路由
import { financialRoutes } from "./routes/financial.js";
await fastify.register(financialRoutes, { prefix: "/api" });

// 导入财务导出路由
import { financialExportRoutes } from "./routes/financialExport.js";
await fastify.register(financialExportRoutes, { prefix: "/api" });

// 导入用户管理路由
import { userRoutes } from "./routes/user.js";
await fastify.register(userRoutes, { prefix: "/api" });

// 导入部门管理路由
import { departmentRoutes } from "./routes/departments.js";
await fastify.register(departmentRoutes, { prefix: "/api/departments" });

// 导入变更记录管理路由
import { changeLogRoutes } from "./routes/changeLog.js";
await fastify.register(changeLogRoutes, { prefix: "/api" });

// 导入角色管理路由
import { roleRoutes } from "./routes/role.js";
await fastify.register(roleRoutes, { prefix: "/api" });

// 导入权限管理路由
import { permissionRoutes } from "./routes/permission.js";
await fastify.register(permissionRoutes, { prefix: "/api" });

// 导入用户权限管理路由
import { userPermissionRoutes } from "./routes/userPermission.js";
await fastify.register(userPermissionRoutes, { prefix: "/api" });

// 导入用户同步调度器
import "./services/userSyncScheduler.js";

// 导入部门同步调度器
import "./services/departmentSyncScheduler.js";

// 导入钉钉Stream服务

// 根路径
fastify.get("/", async () => {
  return {
    message: "钉钉微H5应用后端API服务",
    version: "1.0.0",
    timestamp: new Date().toISOString(),
  };
});

// 健康检查端点
fastify.get("/api/health", async () => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();

    // 检查数据库连接
    const roleCount = await prisma.role.count();
    const permissionCount = await prisma.permission.count();

    await prisma.$disconnect();

    return {
      status: "healthy",
      timestamp: new Date().toISOString(),
      timezone: env.TIMEZONE,
      localTime: new Date().toLocaleString("zh-CN", { timeZone: env.TIMEZONE }),
      database: "connected",
      permissions: {
        roles: roleCount,
        permissions: permissionCount,
        initialized: roleCount > 0 && permissionCount > 0,
      },
      redis: redisService.isReady() ? "connected" : "disconnected",
    };
  } catch (error) {
    return {
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error),
    };
  }
});

// 启动服务器
const start = async () => {
  try {
    // 初始化 Redis 连接
    try {
      await redisService.connect();
      console.log("✅ Redis 连接成功");
    } catch (error) {
      console.warn(
        "⚠️ Redis 连接失败，将使用内存存储:",
        error instanceof Error ? error.message : String(error)
      );
    }

    // 初始化权限中间件
    try {
      const { initializePermissionMiddleware } = await import(
        "./middleware/permission.js"
      );
      const { DatabaseService } = await import("./services/database.js");
      const databaseService = new DatabaseService();
      initializePermissionMiddleware(databaseService);
      console.log("✅ 权限中间件初始化成功");
    } catch (error) {
      console.error("❌ 权限中间件初始化失败:", error);
    }

    // 初始化系统权限和角色（仅在首次启动或需要时）
    try {
      const { PermissionInitializer } = await import(
        "./scripts/initializePermissions.js"
      );
      const initializer = new PermissionInitializer();
      await initializer.initialize();
      console.log("✅ 系统权限和角色初始化成功");
    } catch (error) {
      console.warn(
        "⚠️ 系统权限和角色初始化失败（可能已存在）:",
        error instanceof Error ? error.message : String(error)
      );
    }

    // 初始化钉钉Stream服务
    let streamService: DingTalkStreamServiceV2 | null = null;
    // if (env.NODE_ENV === 'production' || process.env.ENABLE_DINGTALK_STREAM === 'true') {
    if (true) {
      try {
        streamService = new DingTalkStreamServiceV2();

        // 设置事件监听器
        streamService.on("callback", (data: any) => {
          console.log("📨 收到Stream回调事件:", data);
        });

        streamService.on("approval_change", (data: any) => {
          console.log("📋 收到审批变更事件:", data);
        });

        streamService.on("error", (error: any) => {
          console.error("❌ Stream服务错误:", error);
        });

        await streamService.start();
        setGlobalStreamService(streamService as any);
        console.log("✅ 钉钉Stream推送服务V2启动成功");
      } catch (error) {
        console.warn(
          "⚠️ 钉钉Stream推送服务启动失败:",
          error instanceof Error ? error.message : String(error)
        );
      }
    } else {
      console.log("ℹ️ 钉钉Stream推送服务已禁用（开发环境）");
    }

    await fastify.listen({
      port: env.PORT,
      host: env.HOST,
    });

    console.log(`🚀 服务器启动成功！`);
    console.log(`📍 地址: http://${env.HOST}:${env.PORT}`);
    console.log(`🌍 环境: ${env.NODE_ENV}`);
    console.log(`📚 API文档: http://${env.HOST}:${env.PORT}/documentation`);

    // 显示 Redis 状态
    if (redisService.isReady()) {
      console.log(`🔗 Redis: 已连接`);
    } else {
      console.log(`🔗 Redis: 未连接 (使用内存存储)`);
    }
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

// 优雅关闭
process.on("SIGINT", async () => {
  console.log("\n🛑 收到关闭信号，正在优雅关闭服务器...");

  try {
    // 关闭 Fastify 服务器
    await fastify.close();
    console.log("✅ Fastify 服务器已关闭");

    // 关闭 Redis 连接
    await redisService.disconnect();
    console.log("✅ Redis 连接已关闭");
  } catch (error) {
    console.error("❌ 关闭服务时发生错误:", error);
  }

  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n🛑 收到终止信号，正在优雅关闭服务器...");

  try {
    await fastify.close();
    await redisService.disconnect();
  } catch (error) {
    console.error("❌ 关闭服务时发生错误:", error);
  }

  process.exit(0);
});

start();
