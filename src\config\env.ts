import { config } from 'dotenv';
import { z } from 'zod';

// 加载环境变量
config();

// 环境变量验证模式
const envSchema = z.object({
  // 服务器配置
  PORT: z.string().default('3000').transform(Number),
  HOST: z.string().default('0.0.0.0'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  // 钉钉应用配置（可选，用于开发和测试）
  DINGTALK_APP_KEY: z.string().optional(),
  DINGTALK_APP_SECRET: z.string().optional(),
  DINGTALK_CORP_ID: z.string().optional(),
  DINGTALK_AGENT_ID: z.string().optional(),

  // 钉钉API配置
  DINGTALK_API_BASE_URL: z.string().url().default('https://oapi.dingtalk.com'),
  DINGTALK_NEW_API_BASE_URL: z.string().url().default('https://api.dingtalk.com'),

  // 钉钉回调加密配置
  DINGTALK_CALLBACK_TOKEN: z.string().optional(),
  DINGTALK_AES_KEY: z.string().optional(),
  DINGTALK_SUITE_KEY: z.string().optional(),

  // JWT配置
  JWT_SECRET: z.string().optional(),
  JWT_EXPIRES_IN: z.string().default('24h'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),

  // 日志配置
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),

  // 时区配置
  TIMEZONE: z.string().default('Asia/Shanghai'),

  // Redis 配置
  REDIS_URL: z.string().url().optional(),
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().default('6379').transform(Number),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.string().default('0').transform(Number),
  REDIS_TTL_ACCESS_TOKEN: z.string().default('3600').transform(Number), // 1小时
  REDIS_TTL_REFRESH_TOKEN: z.string().default('604800').transform(Number), // 7天
  REDIS_TTL_USER_SESSION: z.string().default('86400').transform(Number), // 24小时

  // 审批配置
  APPROVAL_PROCESS_CODE_PAYMENT: z.string().default('PROC-PAYMENT-001'),
  APPROVAL_PROCESS_CODE_EXPENSE: z.string().default('PROC-EXPENSE-001'),
  APPROVAL_PROCESS_CODE_CONTRACT: z.string().default('PROC-CONTRACT-001'),
});

// 验证环境变量
export const env = envSchema.parse(process.env);
console.log('[ env ] >', env)
// 导出类型
export type Env = z.infer<typeof envSchema>;
