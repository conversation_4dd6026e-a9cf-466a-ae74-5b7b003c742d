import { FastifyInstance } from 'fastify';
import { UserPermissionController } from '../controllers/userPermissionController.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { PERMISSIONS, requirePermission, requireUserAccess } from '../middleware/permission.js';
import { DatabaseService } from '../services/database.js';
import { UserPermissionService } from '../services/userPermissionService.js';

export async function userPermissionRoutes(fastify: FastifyInstance) {
  // 初始化服务和控制器
  const databaseService = new DatabaseService();
  const userPermissionService = new UserPermissionService(databaseService);
  const   userPermissionController = new UserPermissionController(userPermissionService);

  // 获取用户权限信息
  fastify.get('/users/:userid/permissions', {
    preHandler: [jwtAuthMiddleware, requireUserAccess('userid')],
    schema: {
      description: '获取用户权限信息',
      tags: ['UserPermission'],
      params: {
        type: 'object',
        required: ['userid'],
        properties: {
          userid: { type: 'string', description: '用户ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                userid: { type: 'string' },
                permissions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                      module: { type: 'string' },
                      action: { type: 'string' },
                    },
                  },
                },
                roles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
                directRoles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
                inheritedRoles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.getUserPermissions.bind(userPermissionController));

  // 检查用户权限
  fastify.post('/users/:userid/permissions/check', {
    preHandler: [jwtAuthMiddleware, requireUserAccess('userid')],
    schema: {
      description: '检查用户权限',
      tags: ['UserPermission'],
      params: {
        type: 'object',
        required: ['userid'],
        properties: {
          userid: { type: 'string', description: '用户ID' },
        },
      },
      body: {
        type: 'object',
        required: ['permissions'],
        properties: {
          permissions: {
            type: 'array',
            items: { type: 'string' },
            description: '要检查的权限列表',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                userid: { type: 'string' },
                permissions: {
                  type: 'object',
                  additionalProperties: { type: 'boolean' },
                },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.checkUserPermissions.bind(userPermissionController));

  // 获取用户角色
  fastify.get('/users/:userid/roles', {
    preHandler: [jwtAuthMiddleware, requireUserAccess('userid')],
    schema: {
      description: '获取用户角色',
      tags: ['UserPermission'],
      params: {
        type: 'object',
        required: ['userid'],
        properties: {
          userid: { type: 'string', description: '用户ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                directRoles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
                inheritedRoles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.getUserRoles.bind(userPermissionController));

  // 为用户分配角色
  fastify.post('/users/:userid/roles', {
    // preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_ASSIGN)],
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '为用户分配角色',
      tags: ['UserPermission'],
      params: {
        type: 'object',
        required: ['userid'],
        properties: {
          userid: { type: 'string', description: '用户ID' },
        },
      },
      body: {
        type: 'object',
        required: ['roleIds'],
        properties: {
          roleIds: {
            type: 'array',
            items: { type: 'string' },
            description: '角色ID列表',
          },
          expiresAt: { type: 'string', format: 'date-time', description: '过期时间' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.assignUserRoles.bind(userPermissionController));

  // 获取部门角色
  fastify.get('/departments/:deptId/roles', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_READ)],
    schema: {
      description: '获取部门角色',
      tags: ['UserPermission'],
      params: {
        type: 'object',
        required: ['deptId'],
        properties: {
          deptId: { type: 'string', description: '部门ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                deptId: { type: 'number' },
                roles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
                total: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.getDepartmentRoles.bind(userPermissionController));

  // 为部门分配角色
  fastify.post('/departments/:deptId/roles', {
    preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.ROLE_ASSIGN)],
    schema: {
      description: '为部门分配角色',
      tags: ['UserPermission'],
      params: {
        type: 'object',
        required: ['deptId'],
        properties: {
          deptId: { type: 'string', description: '部门ID' },
        },
      },
      body: {
        type: 'object',
        required: ['roleIds'],
        properties: {
          roleIds: {
            type: 'array',
            items: { type: 'string' },
            description: '角色ID列表',
          },
          expiresAt: { type: 'string', format: 'date-time', description: '过期时间' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.assignDepartmentRoles.bind(userPermissionController));

  // 获取当前用户权限信息
  fastify.get('/me/permissions', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取当前用户权限信息',
      tags: ['UserPermission'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                userid: { type: 'string' },
                permissions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                      module: { type: 'string' },
                      action: { type: 'string' },
                    },
                  },
                },
                roles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.getCurrentUserPermissions.bind(userPermissionController));

  // 检查当前用户权限
  fastify.post('/me/permissions/check', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '检查当前用户权限',
      tags: ['UserPermission'],
      body: {
        type: 'object',
        required: ['permissions'],
        properties: {
          permissions: {
            type: 'array',
            items: { type: 'string' },
            description: '要检查的权限列表',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                userid: { type: 'string' },
                permissions: {
                  type: 'object',
                  additionalProperties: { type: 'boolean' },
                },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.checkCurrentUserPermissions.bind(userPermissionController));

  // 获取当前用户角色
  fastify.get('/me/roles', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取当前用户角色',
      tags: ['UserPermission'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                directRoles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
                inheritedRoles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      displayName: { type: 'string' },
                    },
                  },
                },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.getCurrentUserRoles.bind(userPermissionController));

  // 查询当前用户信息
  fastify.get('/me', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '查询当前用户信息',
      tags: ['UserPermission'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                userid: { type: 'string' },
                name: { type: 'string' },
                avatar: { type: 'string' },
                department: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, userPermissionController.getCurrentUserDetail.bind(userPermissionController));
}
