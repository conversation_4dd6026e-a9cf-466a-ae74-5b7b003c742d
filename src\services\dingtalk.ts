import axios, { AxiosInstance } from 'axios';
import crypto from 'crypto';
import { env } from '../config/env.js';
import type {
  ApprovalFormField,
  DingTalkApprovalDetail,
  DingTalkApprovalResponse,
  DingTalkCreateApprovalRequest,
  PaymentApprovalFormData
} from '../types/approval.js';
import type {
  AccessTokenResponse,
  AppConfig,
  DingTalkConfig,
  FileUploadResponse,
  GetDepartmentListResponse,
  GetUserInfoByCodeResponse,
  GetUserResponse,
  JSAPISignature,
  MessageRequest,
  UserListResponse
} from '../types/dingtalk.js';

export class DingTalkService {
  private apiClient: AxiosInstance;
  private newApiClient: AxiosInstance;
  private config: DingTalkConfig;
  private accessToken: string | null = null;
  private tokenExpireTime: number = 0;

  constructor() {
    this.config = {
      appKey: env.DINGTALK_APP_KEY || '',
      appSecret: env.DINGTALK_APP_SECRET || '',
      corpId: env.DINGTALK_CORP_ID || '',
      agentId: env.DINGTALK_AGENT_ID || '',
    };

    // 旧版API客户端
    this.apiClient = axios.create({
      baseURL: env.DINGTALK_API_BASE_URL,
      timeout: 10000,
    });

    // 新版API客户端
    this.newApiClient = axios.create({
      baseURL: env.DINGTALK_NEW_API_BASE_URL,
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  /**
   * 检查钉钉配置是否完整
   */
  private checkConfig(): void {
    if (!this.config.appKey || !this.config.appSecret || !this.config.corpId) {
      throw new Error('钉钉配置不完整，请检查环境变量 DINGTALK_APP_KEY, DINGTALK_APP_SECRET, DINGTALK_CORP_ID');
    }
  }

  /**
   * 检查配置是否可用
   */
  isConfigured(): boolean {
    return !!(this.config.appKey && this.config.appSecret && this.config.corpId);
  }

  private setupInterceptors() {
    // 请求拦截器
    this.apiClient.interceptors.request.use((config) => {
      console.log(`[DingTalk API] ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    });

    // 响应拦截器
    this.apiClient.interceptors.response.use(
      (response) => {
        console.log(`[DingTalk API] Response:`, response.data);
        return response;
      },
      (error) => {
        console.error(`[DingTalk API] Error:`, error.response?.data || error.message);
        throw error;
      }
    );
  }

  /**
   * 获取访问令牌
   */
  async getAccessToken(): Promise<string> {
    this.checkConfig();

    // 检查token是否过期
    if (this.accessToken && Date.now() < this.tokenExpireTime) {
      return this.accessToken;
    }

    try {
      const response = await this.apiClient.get<AccessTokenResponse>('/gettoken', {
        params: {
          appkey: this.config.appKey,
          appsecret: this.config.appSecret,
        },
      });

      if (response.data.errcode !== 0) {
        throw new Error(`获取访问令牌失败: ${response.data.errmsg}`);
      }

      this.accessToken = response.data.access_token!;
      // 提前5分钟过期
      this.tokenExpireTime = Date.now() + (response.data.expires_in! - 300) * 1000;

      return this.accessToken;
    } catch (error) {
      console.error('获取钉钉访问令牌失败:', error);
      throw error;
    }
  }

  /**
   * 通过免登码获取用户信息
   */
  async getUserInfoByCode(authCode: string): Promise<string> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<GetUserInfoByCodeResponse>(
        '/topapi/v2/user/getuserinfo',
        {
          code: authCode,
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取用户信息失败: ${response.data.errmsg}`);
      }

      return response.data.result!.userid;
    } catch (error) {
      console.error('通过免登码获取用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户详细信息
   */
  async getUserDetail(userid: string): Promise<GetUserResponse['result']> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<GetUserResponse>(
        '/topapi/v2/user/get',
        {
          userid,
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取用户详细信息失败: ${response.data.errmsg}`);
      }

      return response.data.result;
    } catch (error) {
      console.error('获取用户详细信息失败:', error);
      throw error;
    }
  }

  /**
   * 通过免登码获取完整用户信息
   */
  async getUserInfoByAuthCode(authCode: string): Promise<GetUserResponse['result'] | null> {
    try {
      // 1. 通过免登码获取用户ID
      const userid = await this.getUserInfoByCode(authCode);

      if (!userid) {
        return null;
      }

      // 2. 通过用户ID获取详细信息
      const userDetail = await this.getUserDetail(userid);

      return userDetail;
    } catch (error) {
      console.error('通过免登码获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 生成JSAPI签名
   */
  async generateJSAPISignature(url: string): Promise<JSAPISignature> {
    // 使用正确的JSAPI签名方法
    return await this.generateCorrectJSAPISignature(url);
  }

  /**
   * 获取部门列表
   */
  async getDepartmentList(): Promise<GetDepartmentListResponse['result']> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<GetDepartmentListResponse>(
        '/topapi/v2/department/listsub',
        {
          dept_id: 1, // 根部门ID
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取部门列表失败: ${response.data.errmsg}`);
      }

      return response.data.result;
    } catch (error) {
      console.error('获取部门列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取应用配置信息（用于前端初始化）
   */
  getAppConfig(): AppConfig {
    return {
      appKey: this.config.appKey || 'demo-app-key',
      corpId: this.config.corpId || 'demo-corp-id',
      agentId: this.config.agentId || 'demo-agent-id',
      jsApiList: [
        'runtime.permission.requestAuthCode',
        'biz.contact.choose',
        'device.notification.confirm',
        'device.notification.alert',
        'device.notification.prompt',
        'biz.ding.post',
        'biz.util.openLink'
      ],
      debug: process.env.NODE_ENV === 'development'
    };
  }

  /**
   * 获取部门用户列表
   */
  async getDepartmentUsers(deptId: number, cursor: number = 0, size: number = 100): Promise<UserListResponse['result']> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<UserListResponse>(
        '/topapi/user/listsimple',
        {
          dept_id: deptId,
          cursor,
          size
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取部门用户列表失败: ${response.data.errmsg}`);
      }

      return response.data.result;
    } catch (error) {
      console.error('获取部门用户列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有用户列表（当没有设置部门时使用）
   */
  async getAllUsers(cursor: number = 0, size: number = 100): Promise<UserListResponse['result']> {
    const accessToken = await this.getAccessToken();

    try {
      // 使用钉钉的用户列表接口，不指定部门ID来获取所有用户
      const response = await this.apiClient.post<UserListResponse>(
        '/topapi/user/listsimple',
        {
          dept_id: 1, // 使用根部门ID，通常可以获取到所有用户
          cursor,
          size,
          contain_access_limit: false // 包含受限用户
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        // 如果根部门方式失败，尝试获取所有部门的用户
        console.warn('通过根部门获取用户失败，尝试遍历所有部门:', response.data.errmsg);
        return await this.getAllUsersByDepartments(cursor, size);
      }

      return response.data.result;
    } catch (error) {
      console.error('获取所有用户列表失败，尝试遍历部门方式:', error);
      // 降级方案：遍历所有部门获取用户
      return await this.getAllUsersByDepartments(cursor, size);
    }
  }

  /**
   * 通过遍历所有部门获取用户列表（降级方案）
   */
  private async getAllUsersByDepartments(cursor: number = 0, size: number = 100): Promise<UserListResponse['result']> {
    try {
      // 获取所有部门
      const departments = await this.getDepartmentList();
      if (!departments || departments.length === 0) {
        return {
          list: [],
          has_more: false,
          next_cursor: 0
        };
      }

      const allUsers: any[] = [];
      const userIds = new Set<string>(); // 用于去重

      // 遍历所有部门获取用户
      for (const dept of departments) {
        try {
          let deptCursor = 0;
          let hasMore = true;

          while (hasMore && allUsers.length < size) {
            const deptUsers = await this.getDepartmentUsers(dept.dept_id, deptCursor, Math.min(100, size - allUsers.length));

            if (deptUsers?.list) {
              // 去重添加用户
              for (const user of deptUsers.list) {
                if (!userIds.has(user.userid)) {
                  userIds.add(user.userid);
                  allUsers.push(user);
                }
              }
            }

            hasMore = deptUsers?.has_more || false;
            deptCursor = deptUsers?.next_cursor || 0;

            // 如果已经获取足够的用户，跳出循环
            if (allUsers.length >= size) {
              break;
            }
          }
        } catch (deptError) {
          console.warn(`获取部门 ${dept.dept_id} 用户失败:`, deptError);
          // 继续处理其他部门
        }
      }

      // 应用分页逻辑
      const startIndex = cursor;
      const endIndex = Math.min(startIndex + size, allUsers.length);
      const paginatedUsers = allUsers.slice(startIndex, endIndex);

      return {
        list: paginatedUsers,
        has_more: endIndex < allUsers.length,
        next_cursor: endIndex < allUsers.length ? endIndex : 0
      };
    } catch (error) {
      console.error('通过部门遍历获取用户失败:', error);
      return {
        list: [],
        has_more: false,
        next_cursor: 0
      };
    }
  }

  /**
   * 发送工作通知
   */
  async sendWorkNotification(message: MessageRequest): Promise<boolean> {
    const accessToken = await this.getAccessToken();
    console.log('[ this.config.agentId ] >', this.config.agentId)
    try {
      const response = await this.apiClient.post(
        '/topapi/message/corpconversation/asyncsend_v2',
        {
          agent_id: this.config.agentId,
          userid_list: message.userIds.join(','),
          msg: {
            msgtype: message.messageType || 'text',
            text: {
              content: message.content
            }
          }
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`发送工作通知失败: ${response.data.errmsg}`);
      }

      return true;
    } catch (error) {
      console.error('发送工作通知失败:', error);
      throw error;
    }
  }

  /**
   * 上传媒体文件
   */
  async uploadMedia(file: Buffer, type: 'image' | 'voice' | 'video' | 'file'): Promise<string> {
    const accessToken = await this.getAccessToken();

    try {
      const formData = new FormData();
      formData.append('media', new Blob([file]));
      formData.append('type', type);

      const response = await this.apiClient.post<FileUploadResponse>(
        '/media/upload',
        formData,
        {
          params: {
            access_token: accessToken,
            type
          },
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`上传媒体文件失败: ${response.data.errmsg}`);
      }

      return response.data.result!.media_id;
    } catch (error) {
      console.error('上传媒体文件失败:', error);
      throw error;
    }
  }

  /**
   * 获取jsapi_ticket（用于正确的JSAPI签名）
   */
  async getJSAPITicket(): Promise<string> {
    try {
      const accessToken = await this.getAccessToken();

      console.log('正在获取JSAPI票据...');

      const response = await this.apiClient.get('/get_jsapi_ticket', {
        params: {
          access_token: accessToken,
        },
      });

      console.log('JSAPI票据响应:', {
        errcode: response.data.errcode,
        errmsg: response.data.errmsg,
        hasTicket: !!response.data.ticket
      });

      if (response.data.errcode !== 0) {
        throw new Error(`获取JSAPI票据失败: ${response.data.errmsg} (错误码: ${response.data.errcode})`);
      }

      if (!response.data.ticket) {
        throw new Error('JSAPI票据为空');
      }

      console.log('JSAPI票据获取成功');
      return response.data.ticket;
    } catch (error) {
      console.error('获取JSAPI票据失败:', error);

      // 检查是否是网络错误
      if (error && typeof error === 'object' && 'code' in error) {
        const errorCode = (error as any).code;
        if (errorCode === 'ECONNREFUSED' || errorCode === 'ENOTFOUND') {
          throw new Error('无法连接到钉钉服务器，请检查网络连接');
        }
      }

      // 检查是否是认证错误
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('40001') || errorMessage.includes('access_token')) {
        throw new Error('访问令牌无效，请检查应用配置');
      }

      // 重新抛出错误，不要返回模拟票据
      throw new Error(`获取JSAPI票据失败: ${errorMessage}`);
    }
  }

  /**
   * 清理URL，移除钉钉调试参数
   */
  private cleanUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      // 移除钉钉调试相关的参数
      const debugParams = [
        'dd_debug_h5',
        'dd_debug_v1',
        'dd_debug_os',
        'dd_debug_v2',
        'dd_debug_unifiedAppId',
        'dd_debug_token',
        'dd_debug_uuid',
        'dd_debug_pid'
      ];

      debugParams.forEach(param => {
        urlObj.searchParams.delete(param);
      });

      return urlObj.toString();
    } catch (error) {
      console.warn('URL清理失败，使用原始URL:', error);
      return url;
    }
  }

  /**
   * 生成正确的JSAPI签名
   */
  async generateCorrectJSAPISignature(url: string): Promise<JSAPISignature> {
    const timestamp = Date.now();
    const nonceStr = crypto.randomBytes(16).toString('hex');

    try {
      const ticket = await this.getJSAPITicket();

      // 1. URL解码
      let decodedUrl = decodeURIComponent(url);

      // 2. 清理调试参数
      decodedUrl = this.cleanUrl(decodedUrl);

      // 3. 按照钉钉官方文档的签名算法
      // 重要：参数必须严格按照字母顺序排列
      const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${decodedUrl}`;
      const signature = crypto.createHash('sha1').update(string1, 'utf8').digest('hex');

      console.log('JSAPI签名生成详情:', {
        originalUrl: url,
        cleanedUrl: decodedUrl,
        ticket: ticket.substring(0, 10) + '...' + ticket.substring(ticket.length - 10), // 部分隐藏ticket
        nonceStr,
        timestamp,
        string1: string1.substring(0, 100) + '...',
        signature
      });

      return {
        agentId: this.config.agentId || '',
        corpId: this.config.corpId,
        timeStamp: timestamp,
        nonceStr,
        signature,
      };
    } catch (error) {
      console.error('生成JSAPI签名失败:', error);
      // 降级到简单签名
      return this.generateJSAPISignature(url);
    }
  }

  /**
   * 发起审批实例
   */
  async createApprovalInstance(request: DingTalkCreateApprovalRequest): Promise<DingTalkApprovalResponse> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<DingTalkApprovalResponse>(
        '/topapi/processinstance/create',
        request,
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`发起审批实例失败: ${response.data.errmsg} (错误码: ${response.data.errcode})`);
      }

      return response.data;
    } catch (error) {
      console.error('发起审批实例失败:', error);
      throw error;
    }
  }

  /**
   * 获取审批实例详情
   */
  async getApprovalInstanceDetail(processInstanceId: string): Promise<DingTalkApprovalDetail> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<DingTalkApprovalDetail>(
        '/topapi/processinstance/get',
        {
          process_instance_id: processInstanceId,
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取审批实例详情失败: ${response.data.errmsg} (错误码: ${response.data.errcode})`);
      }

      return response.data;
    } catch (error) {
      console.error('获取审批实例详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建对公付款审批表单数据
   */
  createPaymentApprovalFormData(data: PaymentApprovalFormData): ApprovalFormField[] {
    const formFields: ApprovalFormField[] = [
      {
        name: '申请人',
        value: data.applicantName,
        componentType: 'TextField'
      },
      {
        name: '申请部门',
        value: '-1',
        componentType: 'TextField'
      },
      {
        name: '所属项目',
        value: data.projectName,
        componentType: 'TextField'
      },
      {
        name: '付款事由',
        value: data.paymentReason,
        componentType: 'TextareaField'
      },
      {
        name: '合同签署主体',
        value: data.contractEntity,
        componentType: 'TextField'
      },
      {
        name: '付款总额',
        value: data.totalAmount.toString(),
        componentType: 'TextField'
      },
      {
        name: '期望付款日期',
        value: data.expectedPaymentDate,
        componentType: 'DateField'
      },
      {
        name: '付款方式',
        value: data.paymentMethod,
        componentType: 'TextField'
      },
      // {
      //   name: '收款账户名称',
      //   value: data.receivingAccount.accountName,
      //   componentType: 'TextField'
      // },
      {
        name: '收款账号',
        value: data.receivingAccount.accountNumber,
        componentType: 'TextField'
      },
      // {
      //   name: '开户银行',
      //   value: data.receivingAccount.bankName,
      //   componentType: 'TextField'
      // }
    ];

    // 可选字段
    if (data.relatedApprovalId) {
      formFields.push({
        name: '关联审批单',
        value: data.relatedApprovalId,
        componentType: 'TextField'
      });
    }

    if (data.receivingAccount.bankCode) {
      formFields.push({
        name: '银行代码',
        value: data.receivingAccount.bankCode,
        componentType: 'TextField'
      });
    }

    if (data.invoiceFiles && data.invoiceFiles.length > 0) {
      formFields.push({
        name: '发票文件',
        value: data.invoiceFiles.join(','),
        componentType: 'TextField'
      });
    }

    if (data.attachments && data.attachments.length > 0) {
      formFields.push({
        name: '附件',
        value: data.attachments.join(','),
        componentType: 'TextField'
      });
    }

    if (data.remark) {
      formFields.push({
        name: '备注',
        value: data.remark,
        componentType: 'TextareaField'
      });
    }

    return formFields;
  }

  /**
   * 发起对公付款审批
   */
  async createPaymentApproval(
    formData: PaymentApprovalFormData,
    originatorUserId: string,
    processCode: string = 'PROC-PAYMENT-001',
  ): Promise<string> {
    const formFields = this.createPaymentApprovalFormData(formData);

    const request: DingTalkCreateApprovalRequest = {
      process_code: processCode,
      originator_user_id: originatorUserId,
      dept_id: -1, // 根部门ID，实际使用时应该从用户信息中获取
      form_component_values: formFields
    };

    const response = await this.createApprovalInstance(request);

    if (!response.process_instance_id) {
      throw new Error('发起审批失败：未返回审批实例ID');
    }

    return response.process_instance_id;
  }
}
