import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { ApprovalService } from '../services/approval.js';
import { ProjectService } from '../services/project.js';
import { ContractEntity, PaymentMethod } from '../types/approval.js';
import {
    ServiceType,
    TaxRate,
    WeeklyBudgetStatus
} from '../types/project.js';

// 验证模式
const createWeeklyBudgetSchema = z.object({
  title: z.string().min(1, '预算标题不能为空').max(200, '预算标题不能超过200字符'),
  weekStartDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的开始日期格式'),
  weekEndDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的结束日期格式'),
  serviceType: z.nativeEnum(ServiceType, { errorMap: () => ({ message: '无效的服务类型' }) }),
  serviceContent: z.string().min(1, '服务内容不能为空'),
  remarks: z.string().optional(),
  contractAmount: z.number().min(0, '合同金额不能为负数'),
  taxRate: z.nativeEnum(TaxRate, { errorMap: () => ({ message: '无效的税率' }) }),
  supplierId: z.string().optional(),
});

const updateWeeklyBudgetSchema = z.object({
  id: z.string().min(1, '预算ID不能为空'),
  title: z.string().min(1).max(200).optional(),
  weekStartDate: z.string().refine((date) => !isNaN(Date.parse(date))).optional(),
  weekEndDate: z.string().refine((date) => !isNaN(Date.parse(date))).optional(),
  serviceType: z.nativeEnum(ServiceType).optional(),
  serviceContent: z.string().min(1).optional(),
  remarks: z.string().optional(),
  contractAmount: z.number().min(0).optional(),
  taxRate: z.nativeEnum(TaxRate).optional(),
  status: z.nativeEnum(WeeklyBudgetStatus).optional(),
  paidAmount: z.number().min(0).optional(),
  supplierId: z.string().optional(),
});

const weeklyBudgetQuerySchema = z.object({
  page: z.string().transform(Number).optional(),
  pageSize: z.string().transform(Number).optional(),
  projectId: z.string().optional(),
  supplierId: z.string().optional(),
  serviceType: z.nativeEnum(ServiceType).optional(),
  status: z.nativeEnum(WeeklyBudgetStatus).optional(),
  year: z.string().transform(Number).optional(),
  weekNumber: z.string().transform(Number).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  sortBy: z.enum(['weekStartDate', 'contractAmount', 'createdAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

export class WeeklyBudgetController {
  private projectService: ProjectService;
  private approvalService: ApprovalService;

  constructor() {
    this.projectService = new ProjectService();
    this.approvalService = new ApprovalService();
  }

  /**
   * 创建周预算
   */
  async createWeeklyBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { projectId } = request.params as { projectId: string };
      const budgetData = createWeeklyBudgetSchema.parse(request.body);

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user';

      const budget = await this.projectService.createWeeklyBudget(projectId, budgetData, createdBy);

      return reply.send({
        success: true,
        data: budget,
        message: '创建周预算成功'
      });
    } catch (error) {
      console.error('创建周预算失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '创建周预算失败'
      });
    }
  }

  /**
   * 获取周预算列表
   */
  async getWeeklyBudgets(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = weeklyBudgetQuerySchema.parse(request.query);
      const result = await this.projectService.getWeeklyBudgets(queryParams);

      return reply.send({
        success: true,
        data: result,
        message: '获取周预算列表成功'
      });
    } catch (error) {
      console.error('获取周预算列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取周预算列表失败'
      });
    }
  }

  /**
   * 获取单个周预算
   */
  async getWeeklyBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const budget = await this.projectService.getWeeklyBudget(id);

      if (!budget) {
        return reply.status(404).send({
          success: false,
          message: '周预算不存在'
        });
      }

      return reply.send({
        success: true,
        data: budget,
        message: '获取周预算成功'
      });
    } catch (error) {
      console.error('获取周预算失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取周预算失败'
      });
    }
  }

  /**
   * 更新周预算
   */
  async updateWeeklyBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const budgetData = updateWeeklyBudgetSchema.parse(request.body);

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const updatedBy = user?.userid || 'current-user';

      const budget = await this.projectService.updateWeeklyBudget(budgetData, updatedBy);

      return reply.send({
        success: true,
        data: budget,
        message: '更新周预算成功'
      });
    } catch (error) {
      console.error('更新周预算失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '更新周预算失败'
      });
    }
  }

  /**
   * 删除周预算
   */
  async deleteWeeklyBudget(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      await this.projectService.deleteWeeklyBudget(id);

      return reply.send({
        success: true,
        message: '删除周预算成功'
      });
    } catch (error) {
      console.error('删除周预算失败:', error);
      return reply.status(500).send({
        success: false,
        message: '删除周预算失败'
      });
    }
  }

  /**
   * 获取周预算统计
   */
  async getWeeklyBudgetStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = await this.projectService.getWeeklyBudgetStats();

      return reply.send({
        success: true,
        data: stats,
        message: '获取周预算统计成功'
      });
    } catch (error) {
      console.error('获取周预算统计失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取周预算统计失败'
      });
    }
  }

  /**
   * 批量创建项目周预算
   */
  async batchCreateWeeklyBudgets(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { projectId } = request.params as { projectId: string };
      const { startDate, endDate, serviceType, defaultContractAmount, defaultTaxRate } = request.body as {
        startDate: string;
        endDate: string;
        serviceType: ServiceType;
        defaultContractAmount: number;
        defaultTaxRate: TaxRate;
      };

      // 获取当前用户ID（从JWT认证中获取）
      const user = (request as any).user;
      const createdBy = user?.userid || 'current-user';

      const budgets = await this.projectService.batchCreateWeeklyBudgets(
        projectId,
        startDate,
        endDate,
        serviceType,
        defaultContractAmount,
        defaultTaxRate,
        createdBy
      );

      return reply.send({
        success: true,
        data: budgets,
        message: '批量创建周预算成功'
      });
    } catch (error) {
      console.error('批量创建周预算失败:', error);
      return reply.status(500).send({
        success: false,
        message: '批量创建周预算失败'
      });
    }
  }

  /**
   * 发起周预算对公付款审批
   */
  async createPaymentApproval(request: FastifyRequest, reply: FastifyReply) {
    try {
      const createApprovalSchema = z.object({
        weeklyBudgetId: z.string().min(1, '周预算ID不能为空'),
        totalAmount: z.number().min(0.01, '付款总额必须大于0'),
        paymentReason: z.string().min(1, '付款事由不能为空'),
        department: z.number().min(1, '申请部门不能为空'),
        contractEntity: z.nativeEnum(ContractEntity).default(ContractEntity.COMPANY_A),
        expectedPaymentDate: z.string().min(1, '期望付款时间不能为空'),
        paymentMethod: z.nativeEnum(PaymentMethod).default(PaymentMethod.BANK_TRANSFER),
        receivingAccount: z.object({
          accountName: z.string().min(1, '账户名称不能为空'),
          accountNumber: z.string().min(1, '账号不能为空'),
          bankName: z.string().min(1, '开户银行不能为空'),
          bankCode: z.string().optional()
        }),
        relatedApprovalId: z.string().optional(),
        invoiceFiles: z.array(z.string()).optional(),
        attachments: z.array(z.string()).optional(),
        remark: z.string().optional(),

        // 兼容旧版本字段
        approvalAmount: z.number().optional(),
        reason: z.string().optional()
      });

      const approvalData = createApprovalSchema.parse(request.body);

      // 从JWT认证中间件获取当前用户信息
      const user = (request as any).user;
      if (!user) {
        return reply.status(401).send({
          success: false,
          message: '用户未认证',
          code: 'USER_NOT_AUTHENTICATED'
        });
      }

      const originatorUserId = user.userid;

      const approval = await this.approvalService.createPaymentApproval(
        approvalData,
        originatorUserId
      );

      return reply.send({
        success: true,
        data: approval,
        message: '发起审批成功'
      });
    } catch (error) {
      console.error('发起审批失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '发起审批失败'
      });
    }
  }
}
