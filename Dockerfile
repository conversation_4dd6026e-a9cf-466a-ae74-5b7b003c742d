# 多阶段构建 - 优化版本
# 第一阶段：构建阶段
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 一次性配置环境和安装pnpm（减少层数）
ENV NODE_ENV=production
RUN npm config set registry https://registry.npmmirror.com && \
  npm install -g pnpm@latest --registry=https://registry.npmmirror.com && \
  pnpm config set registry https://registry.npmmirror.com

# 优化：先复制依赖文件，利用Docker层缓存
COPY package.json pnpm-lock.yaml* ./
COPY .env.prod ./.env.prod

# 调试：显示文件信息
RUN echo "=== 检查文件 ===" && \
  ls -la && \
  echo "=== package.json 内容 ===" && \
  head -20 package.json && \
  echo "=== pnpm 版本 ===" && \
  pnpm --version

# 安装依赖（这层会被缓存，除非package.json改变）
RUN pnpm install --prefer-frozen-lockfile || \
  (echo "=== 安装失败，尝试不使用 lockfile ===" && pnpm install)

# 复制Prisma schema并生成客户端
COPY prisma ./prisma/
RUN pnpm db:generate

# 最后复制源代码（避免代码变更影响依赖缓存）
COPY src ./src/
COPY tsconfig.json ./

# 构建应用
RUN pnpm run build
# 第二阶段：运行阶段
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS runner

# 设置工作目录
WORKDIR /app

# 一次性安装系统依赖、配置npm和安装pnpm（减少层数）
RUN apk add --no-cache tzdata curl && \
  npm config set registry https://registry.npmmirror.com && \
  npm install -g pnpm@latest --registry=https://registry.npmmirror.com && \
  pnpm config set registry https://registry.npmmirror.com

# 创建非root用户（提前创建，避免后续权限问题）
RUN addgroup -g 1001 -S nodejs && \
  adduser -S nodejs -u 1001

# 复制依赖文件并安装生产依赖
COPY package.json pnpm-lock.yaml* ./
RUN pnpm install --prod --prefer-frozen-lockfile && \
  pnpm store prune

# 复制Prisma schema并生成客户端
COPY prisma ./prisma/
RUN pnpm db:generate

# 复制构建后的应用和配置文件
COPY --from=builder /app/dist ./dist
COPY .env.prod ./
COPY start.sh ./start.sh

# 设置文件权限并切换用户
RUN chown -R nodejs:nodejs /app && \
  chmod +x /app/start.sh
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 设置环境变量
ENV NODE_ENV=production
ENV TZ=Asia/Shanghai
ENV TIMEZONE=Asia/Shanghai

# 启动应用
CMD ["./start.sh"]
