# 多阶段构建
# 第一阶段：构建阶段
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS builder

# 配置npm镜像源并安装pnpm
RUN npm config set registry https://registry.npmmirror.com && \
  npm install -g pnpm --registry=https://registry.npmmirror.com

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./
COPY .env.prod ./.env
# 配置pnpm镜像源并安装所有依赖（包括devDependencies，构建时需要）
RUN pnpm config set registry https://registry.npmmirror.com && \
  pnpm install

# 复制Prisma schema
COPY prisma ./prisma/

# 生成Prisma客户端
RUN pnpm db:generate

# 复制源代码
COPY . .

# 构建应用
RUN pnpm run build
# 第二阶段：运行阶段
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS runner

# 安装时区数据和curl（用于健康检查）
RUN apk add --no-cache tzdata curl

# 配置npm镜像源并安装pnpm
RUN npm config set registry https://registry.npmmirror.com && \
  npm install -g pnpm

# 设置工作目录
WORKDIR /app

# 复制package.json
COPY package.json ./
COPY pnpm-lock.yaml* ./

# 配置pnpm镜像源并只安装生产依赖
RUN pnpm config set registry https://registry.npmmirror.com && \
  pnpm install --prod

# 复制Prisma schema
COPY prisma ./prisma/

# 重新生成Prisma客户端（在运行阶段）
RUN pnpm db:generate

# 复制构建后的应用
COPY --from=builder /app/dist ./dist

# 复制环境变量文件
COPY .env ./

# 复制启动脚本
COPY start.sh ./start.sh

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 更改文件所有权
RUN chown -R nodejs:nodejs /app
RUN chmod +x /app/start.sh
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 设置环境变量
ENV NODE_ENV=production
ENV TZ=Asia/Shanghai
ENV TIMEZONE=Asia/Shanghai

# 启动应用
CMD ["./start.sh"]
