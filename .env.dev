# 开发环境配置

# 应用配置
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
LOG_LEVEL=debug

# 数据库配置
DATABASE_URL=**************************************************/cantv_ding_dev
POSTGRES_DB=cantv_ding_dev
POSTGRES_USER=cantv_user
POSTGRES_PASSWORD=dev_password

# 钉钉应用配置（开发环境）
DINGTALK_APP_KEY=your_dev_dingtalk_app_key
DINGTALK_APP_SECRET=your_dev_dingtalk_app_secret
DINGTALK_CORP_ID=your_dev_dingtalk_corp_id
DINGTALK_AGENT_ID=your_dev_dingtalk_agent_id

# 钉钉API地址
DINGTALK_API_BASE_URL=https://oapi.dingtalk.com
DINGTALK_NEW_API_BASE_URL=https://api.dingtalk.com

# JWT配置
JWT_SECRET=dev_jwt_secret_key_for_development_only
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Redis配置
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=

# 镜像配置
IMAGE_TAG=latest

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 文件上传配置
MAX_FILE_SIZE=50MB
UPLOAD_PATH=/app/uploads

# 安全配置（开发环境较宽松）
CORS_ORIGIN=*
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=1000
