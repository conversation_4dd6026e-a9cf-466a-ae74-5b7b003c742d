import { APPROVAL_CONFIG } from '../config/approval.js';
import {
  ApprovalInstance,
  ApprovalListResponse,
  ApprovalQueryParams,
  ApprovalStats,
  ApprovalStatus,
  CreateApprovalRequest,
  PaymentApprovalFormData
} from '../types/approval.js';
import { DatabaseService } from './database.js';
import { dingTalkEncryptService } from './dingtalk-encrypt.js';
import { DingTalkService } from './dingtalk.js';





export class ApprovalService {
  private dingTalkService: DingTalkService;
  private databaseService: DatabaseService;

  constructor() {
    this.dingTalkService = new DingTalkService();
    this.databaseService = new DatabaseService();
  }

  /**
   * 发起对公付款审批
   */
  async createPaymentApproval(request: CreateApprovalRequest, originatorUserId: string): Promise<ApprovalInstance> {
    try {
      // 1. 获取周预算信息
      const weeklyBudget = await this.databaseService.getWeeklyBudget(request.weeklyBudgetId);
      if (!weeklyBudget) {
        throw new Error('周预算不存在');
      }

      // 2. 检查审批金额是否合理
      const totalAmount = request.totalAmount || request.approvalAmount || 0;
      if (totalAmount <= 0) {
        throw new Error('付款总额必须大于0');
      }

      if (totalAmount > weeklyBudget.contractAmount) {
        throw new Error('付款总额不能超过合同金额');
      }

      // 3. 获取项目和供应商信息
      // 注意：这里需要根据实际的数据库关系来获取项目和供应商信息
      const project = null; // TODO: 通过projectId获取项目信息
      const supplier = null; // TODO: 通过supplierId获取供应商信息

      // 4. 获取申请人详细信息
      const applicantInfo = await this.dingTalkService.getUserDetail(originatorUserId);

      // 5. 构建审批表单数据
      const formData: PaymentApprovalFormData = {
        title: `项目 - ${weeklyBudget.title} - 对公付款申请`,
        applicant: originatorUserId,
        applicantName: applicantInfo?.name || '未知用户',
        department: request.department || 1,
        relatedApprovalId: request.relatedApprovalId,
        projectId: weeklyBudget.projectId,
        projectName: '未知项目', // TODO: 通过projectId获取项目名称
        paymentReason: request.paymentReason || request.reason || '周预算付款',
        contractEntity: request.contractEntity || APPROVAL_CONFIG.CONTRACT_ENTITIES.COMPANY_A,
        totalAmount: totalAmount,
        expectedPaymentDate: request.expectedPaymentDate ?? new Date().toISOString().split('T')[0],
        paymentMethod: request.paymentMethod || APPROVAL_CONFIG.PAYMENT_METHODS.BANK_TRANSFER,
        receivingAccount: request.receivingAccount || {
          accountName: '收款方', // TODO: 通过supplierId获取供应商名称
          accountNumber: '待填写', // TODO: 通过supplierId获取银行账户
          bankName: '待填写', // TODO: 通过supplierId获取银行名称
          bankCode: undefined
        },
        invoiceFiles: request.invoiceFiles,
        attachments: request.attachments,
        attachmentFiles: request.attachmentFiles, // 添加附件文件支持
        remark: request.remark
      };

      // 5. 发起钉钉审批
      console.log('[ formData ] >', formData)
      console.log('[ APPROVAL_CONFIG.PROCESS_CODES.PAYMENT ] >', APPROVAL_CONFIG.PROCESS_CODES.PAYMENT)
      console.log('[ originatorUserId ] >', originatorUserId)
      const processInstanceId = await this.dingTalkService.createPaymentApproval(
        formData,
        originatorUserId,
        request.department,
        APPROVAL_CONFIG.PROCESS_CODES.PAYMENT,
      );

      // 6. 创建审批实例记录
      const approvalInstance = await this.databaseService.createApprovalInstance({
        processInstanceId,
        processCode: APPROVAL_CONFIG.PROCESS_CODES.PAYMENT,
        businessId: request.weeklyBudgetId,
        title: formData.title,
        originatorUserId,
        status: 'PENDING' as ApprovalStatus,
        createTime: new Date(),
        approvalAmount: totalAmount,
        reason: request.paymentReason || request.reason,
        remark: request.remark,
        weeklyBudgetId: request.weeklyBudgetId
      });

      // 7. 更新周预算的审批状态
      await this.updateWeeklyBudgetApprovalStatus(
        request.weeklyBudgetId,
        'PENDING' as ApprovalStatus,
        totalAmount,
        request.paymentReason || request.reason
      );

      return approvalInstance;
    } catch (error) {
      console.error('发起审批失败:', error);
      throw error;
    }
  }

  /**
   * 获取审批实例列表
   */
  async getApprovalInstances(params: ApprovalQueryParams = {}): Promise<ApprovalListResponse> {
    return await this.databaseService.getApprovalInstances(params);

  }

  /**
   * 获取单个审批实例
   */
  async getApprovalInstance(id: string): Promise<ApprovalInstance | null> {
    // TODO: 实现数据库审批实例查询方法
    return await this.databaseService.getApprovalInstance(id);
  }

  /**
   * 同步审批状态
   */
  async syncApprovalStatus(processInstanceId: string): Promise<ApprovalInstance | null> {
    const prisma = (this.databaseService as any).prisma;

    try {
      // 使用数据库事务确保同步操作的原子性
      return await prisma.$transaction(async (tx: any) => {
        // 1. 获取当前数据库中的审批实例状态
        const currentApproval = await tx.approvalInstance.findUnique({
          where: { processInstanceId },
          select: {
            id: true,
            status: true,
            weeklyBudgetId: true,
            processInstanceId: true,
            originatorUserId: true
          }
        });

        // 2. 从钉钉获取最新状态
        const approvalDetail = await this.dingTalkService.getApprovalInstanceDetail(processInstanceId);
        if (!approvalDetail.process_instance) {
          throw new Error('审批实例不存在');
        }

        // 3. 获取审批中的付款金额
        const actualAmount = approvalDetail.process_instance.form_component_values.find(
          (field) => field.name === APPROVAL_CONFIG.FORM_FIELDS.PAYMENT.TOTAL_AMOUNT
        )?.value as number;

        const newStatus = this.mapDingTalkStatusToLocal(approvalDetail.process_instance.status);
        const oldStatus = currentApproval?.status;

        console.log(`🔄 同步审批状态: ${processInstanceId}`);
        console.log(`- 钉钉状态: ${approvalDetail.process_instance.status} -> 本地状态: ${newStatus}`);
        console.log(`- 状态变化: ${oldStatus} -> ${newStatus}`);
        console.log(`- 实际金额: ${actualAmount}`);

        // 4. 更新或创建审批实例状态
        const updateData: any = {
          status: newStatus,
          result: approvalDetail.process_instance.result,
          finishTime: approvalDetail.process_instance.finish_time
            ? new Date(approvalDetail.process_instance.finish_time)
            : undefined
        };

        if (actualAmount !== undefined) {
          updateData.actualAmount = actualAmount;
        }

        // 如果审批实例不存在，需要创建它
        let approval;
        if (!currentApproval) {
          console.log(`⚠️ 审批实例 ${processInstanceId} 不存在，尝试创建...`);

          // 从钉钉获取更多信息来创建审批实例
          const createData = {
            processInstanceId,
            processCode: approvalDetail.process_instance.business_id || 'PROC-PAYMENT-001',
            title: approvalDetail.process_instance.title || '对公付款审批',
            originatorUserId: approvalDetail.process_instance.originator_userid,
            status: newStatus,
            result: approvalDetail.process_instance.result,
            createTime: new Date(approvalDetail.process_instance.create_time),
            finishTime: approvalDetail.process_instance.finish_time
              ? new Date(approvalDetail.process_instance.finish_time)
              : null,
            approvalAmount: actualAmount || 0,
            actualAmount: actualAmount,
            weeklyBudgetId: '', // 这里需要根据业务逻辑确定
            reason: '通过Stream回调创建'
          };

          // 尝试从审批表单中获取周预算ID
          const weeklyBudgetIdField = approvalDetail.process_instance.form_component_values.find(
            (field) => field.name === 'weeklyBudgetId' || field.name === '周预算ID'
          );

          if (weeklyBudgetIdField?.value) {
            createData.weeklyBudgetId = weeklyBudgetIdField.value as string;
          } else {
            console.warn('⚠️ 无法从审批表单中获取周预算ID，跳过创建审批实例');
            return null;
          }

          approval = await tx.approvalInstance.create({
            data: createData,
            include: {
              weeklyBudget: {
                include: {
                  project: true,
                  supplier: true
                }
              }
            }
          });

          console.log(`✅ 创建审批实例成功: ${processInstanceId}`);
        } else {
          // 更新现有审批实例
          approval = await tx.approvalInstance.update({
            where: { processInstanceId },
            data: updateData,
            include: {
              weeklyBudget: {
                include: {
                  project: true,
                  supplier: true
                }
              }
            }
          });
        }

        // 5. 只有当状态从非APPROVED变为APPROVED时，才更新周预算
        if (newStatus === 'APPROVED' && oldStatus !== 'APPROVED') {
          console.log(`✅ 审批状态从 ${oldStatus} 变更为 ${newStatus}，需要更新周预算`);

          // 在同一个事务中更新周预算
          await this.updateWeeklyBudgetInTransaction(tx, approval);
        } else if (newStatus === 'APPROVED' && oldStatus === 'APPROVED') {
          console.log(`⚠️ 审批状态已经是 APPROVED，跳过周预算更新，避免重复叠加金额`);
        } else {
          console.log(`ℹ️ 审批状态变更为 ${newStatus}，无需更新周预算`);
        }

        // 转换为标准格式返回
        return (this.databaseService as any).transformApprovalInstance(approval);
      });
    } catch (error) {
      console.error('❌ 同步审批状态失败:', error);
      return null;
    }
  }

  /**
   * 更新周预算的审批状态
   */
  private async updateWeeklyBudgetApprovalStatus(
    weeklyBudgetId: string,
    approvalStatus: ApprovalStatus,
    approvalAmount: number,
    approvalReason?: string
  ): Promise<void> {
    try {
      // 直接使用 Prisma 更新，绕过类型限制
      await (this.databaseService as any).prisma.weeklyBudget.update({
        where: { id: weeklyBudgetId },
        data: {
          approvalStatus,
          approvalAmount: new (await import('@prisma/client/runtime/library')).Decimal(approvalAmount),
          approvalReason
        }
      });

      console.log(`更新周预算 ${weeklyBudgetId} 审批状态: ${approvalStatus}`);
    } catch (error) {
      console.error('更新周预算审批状态失败:', error);
      throw error;
    }
  }

  /**
   * 在事务中更新周预算（防止重复叠加）
   */
  private async updateWeeklyBudgetInTransaction(tx: any, approval: any): Promise<void> {
    try {
      // 1. 获取当前周预算信息
      const weeklyBudget = await tx.weeklyBudget.findUnique({
        where: { id: approval.weeklyBudgetId },
        select: {
          id: true,
          paidAmount: true,
          contractAmount: true,
          approvalInstances: {
            where: {
              status: 'APPROVED'
            },
            select: {
              processInstanceId: true,
              actualAmount: true,
              approvalAmount: true
            }
          }
        }
      });

      if (!weeklyBudget) {
        throw new Error('周预算不存在');
      }

      // 2. 重新计算所有已通过审批的总金额
      let totalApprovedAmount = 0;
      const processedApprovals = new Set<string>();

      for (const approvalInst of weeklyBudget.approvalInstances) {
        if (!processedApprovals.has(approvalInst.processInstanceId)) {
          const amount = Number(approvalInst.actualAmount || approvalInst.approvalAmount || 0);
          totalApprovedAmount += amount;
          processedApprovals.add(approvalInst.processInstanceId);
        }
      }

      console.log(`🔄 在事务中更新周预算 ${approval.weeklyBudgetId}:`);
      console.log(`- 重新计算的总已付金额: ${totalApprovedAmount}`);
      console.log(`- 原已付金额: ${weeklyBudget.paidAmount}`);

      // 3. 更新周预算
      await tx.weeklyBudget.update({
        where: { id: approval.weeklyBudgetId },
        data: {
          paidAmount: totalApprovedAmount,
          approvalStatus: 'APPROVED',
          status: 'EXECUTING'
        }
      });

      console.log(`✅ 周预算已付金额更新为: ${totalApprovedAmount}`);
    } catch (error) {
      console.error('❌ 在事务中更新周预算失败:', error);
      throw error;
    }
  }

  /**
   * 审批通过后更新周预算（使用数据库事务确保原子性）
   */
  private async updateWeeklyBudgetAfterApproval(approvalInstance: ApprovalInstance): Promise<void> {
    // 使用数据库事务确保操作的原子性
    const prisma = (this.databaseService as any).prisma;

    try {
      await prisma.$transaction(async (tx: any) => {
        // 1. 检查审批实例是否已经处理过（通过查询审批实例的更新时间和状态）
        const currentApproval = await tx.approvalInstance.findUnique({
          where: { processInstanceId: approvalInstance.processInstanceId },
          select: {
            id: true,
            status: true,
            actualAmount: true,
            approvalAmount: true,
            weeklyBudgetId: true,
            updatedAt: true
          }
        });

        if (!currentApproval || currentApproval.status !== 'APPROVED') {
          console.log(`审批实例 ${approvalInstance.processInstanceId} 状态不是APPROVED，跳过周预算更新`);
          return;
        }

        // 2. 获取当前周预算信息
        const weeklyBudget = await tx.weeklyBudget.findUnique({
          where: { id: approvalInstance.weeklyBudgetId },
          select: {
            id: true,
            paidAmount: true,
            contractAmount: true,
            approvalInstances: {
              where: {
                status: 'APPROVED',
                processInstanceId: { not: approvalInstance.processInstanceId }
              },
              select: { processInstanceId: true, actualAmount: true, approvalAmount: true }
            }
          }
        });

        if (!weeklyBudget) {
          throw new Error('周预算不存在');
        }

        // 3. 计算当前应该有的已付金额（重新计算所有已通过的审批）
        let totalApprovedAmount = 0;

        // 累加所有其他已通过的审批金额
        for (const otherApproval of weeklyBudget.approvalInstances) {
          const amount = Number(otherApproval.actualAmount || otherApproval.approvalAmount || 0);
          totalApprovedAmount += amount;
        }

        // 加上当前审批的金额
        const currentPaymentAmount = Number(approvalInstance.actualAmount || approvalInstance.approvalAmount || 0);
        totalApprovedAmount += currentPaymentAmount;

        // 4. 安全检查
        if (currentPaymentAmount <= 0) {
          console.warn(`审批实例 ${approvalInstance.id} 的付款金额无效: ${currentPaymentAmount}，跳过周预算更新`);
          return;
        }

        if (totalApprovedAmount > Number(weeklyBudget.contractAmount)) {
          console.warn(`警告：总已付金额 ${totalApprovedAmount} 超过合同金额 ${weeklyBudget.contractAmount}`);
        }

        console.log(`🔄 使用事务更新周预算 ${approvalInstance.weeklyBudgetId}:`);
        console.log(`- 审批实例ID: ${approvalInstance.id}`);
        console.log(`- 钉钉审批ID: ${approvalInstance.processInstanceId}`);
        console.log(`- 当前审批金额: ${currentPaymentAmount}`);
        console.log(`- 原已付金额: ${weeklyBudget.paidAmount}`);
        console.log(`- 重新计算的总金额: ${totalApprovedAmount}`);

        // 5. 更新周预算的已付金额为重新计算的总金额
        await tx.weeklyBudget.update({
          where: { id: approvalInstance.weeklyBudgetId },
          data: {
            paidAmount: totalApprovedAmount,
            approvalStatus: 'APPROVED',
            approvalAmount: currentPaymentAmount,
            approvalReason: `审批已通过，已付金额已更新 (+${currentPaymentAmount})`
          }
        });

        console.log(`✅ 周预算更新成功，已付金额更新为: ${totalApprovedAmount}`);
      });
    } catch (error) {
      console.error('❌ 更新周预算失败:', error);
      throw error;
    }
  }

  /**
   * 获取审批统计信息
   */
  async getApprovalStats(params?: { startDate?: string; endDate?: string }): Promise<ApprovalStats> {
    // TODO: 实现审批统计查询
    console.log('获取审批统计信息:', params);
    return {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      cancelled: 0,
      totalAmount: 0,
      approvedAmount: 0,
      pendingAmount: 0
    };
  }

  /**
   * 处理审批回调事件（带加密验证）
   */
  async handleApprovalCallback(callbackParams: {
    signature: string;
    timestamp: string;
    nonce: string;
    encrypt: string;
  }): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      // 1. 验证和解密回调数据
      const decryptResult = dingTalkEncryptService.processCallback(callbackParams);
      if (!decryptResult.success) {
        console.error('回调验证失败:', decryptResult.error);
        return { success: false, message: decryptResult.error || '回调验证失败' };
      }

      const callbackData = decryptResult.data;
      console.log('收到钉钉回调:', callbackData);

      // 2. 处理不同类型的回调事件
      if (callbackData.EventType === 'bpms_instance_change') {
        // 审批实例状态变更
        const processInstanceId = callbackData.processInstanceId;
        if (!processInstanceId) {
          throw new Error('审批回调事件缺少processInstanceId');
        }

        // 同步审批状态
        const approval = await this.syncApprovalStatus(processInstanceId);
        if (approval) {
          // 如果审批通过，更新相关业务数据
          if (approval.status === 'APPROVED') {
            await this.updateWeeklyBudgetAfterApproval(approval);
          }
        }

        console.log(`处理审批回调事件成功: ${processInstanceId}`);
        return {
          success: true,
          message: '回调处理成功',
          data: { processInstanceId, status: approval?.status }
        };
      } else if(callbackData.EventType === 'bpms_task_change') {
        console.log('处理任务回调事件:', callbackData);
        // 审批实例状态变更
        const processInstanceId = callbackData.processInstanceId;
        if (!processInstanceId) {
          throw new Error('审批回调事件缺少processInstanceId');
        }
        if (callbackData.type === 'finish' && callbackData.result === 'agree') {
          // 同步审批状态
          const approval = await this.syncApprovalStatus(processInstanceId);
          if (approval) {
            // 如果审批通过，更新相关业务数据
            if (approval.status === 'APPROVED') {
              await this.updateWeeklyBudgetAfterApproval(approval);
              console.log(`处理审批回调事件成功: ${processInstanceId}`);
              return {
                success: true,
                message: '回调处理成功',
                data: { processInstanceId, status: approval?.status }
              };
            }
          }
        }
        return { success: true, message: '回调处理成功' };

      } else {
        console.log(`未处理的回调事件类型: ${callbackData.EventType}`);
        return { success: true, message: '事件类型未处理，但回调验证成功' };
      }
    } catch (error) {
      console.error('处理审批回调事件失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '处理回调失败'
      };
    }
  }

  /**
   * 生成回调响应（加密）
   */
  generateCallbackResponse(responseData: any): any {
    try {
      const response = dingTalkEncryptService.generateCallbackResponse(
        JSON.stringify(responseData)
      );

      if (!response) {
        throw new Error('生成回调响应失败');
      }

      return response;
    } catch (error) {
      console.error('生成回调响应失败:', error);
      return {
        msg_signature: '',
        timeStamp: Date.now().toString(),
        nonce: '',
        encrypt: ''
      };
    }
  }

  /**
   * 映射钉钉审批状态到本地状态
   */
  private mapDingTalkStatusToLocal(dingTalkStatus: string): ApprovalStatus {
    switch (dingTalkStatus) {
      case 'NEW':
      case 'RUNNING':
        return ApprovalStatus.PENDING;
      case 'COMPLETED':
        return ApprovalStatus.APPROVED;
      case 'TERMINATED':
        return ApprovalStatus.REJECTED;
      case 'CANCELED':
        return ApprovalStatus.CANCELLED;
      default:
        return ApprovalStatus.PENDING;
    }
  }



  /**
   * 获取服务类型标签
   */
  private getServiceTypeLabel(serviceType: string): string {
    switch (serviceType) {
      case 'INFLUENCER':
        return '达人服务';
      case 'ADVERTISING':
        return '投流服务';
      case 'OTHER':
        return '其他服务';
      default:
        return serviceType;
    }
  }

  /**
   * 获取税率标签
   */
  private getTaxRateLabel(taxRate: string): string {
    switch (taxRate) {
      case 'SPECIAL_1':
        return '专票1%';
      case 'SPECIAL_3':
        return '专票3%';
      case 'SPECIAL_6':
        return '专票6%';
      case 'GENERAL':
        return '普票';
      default:
        return taxRate;
    }
  }

  /**
   * 处理Stream推送的审批状态变更
   */
  async handleApprovalStatusChange(data: {
    processInstanceId: string;
    result: string;
    type: string;
    staffId: string;
    createTime: number;
    finishTime?: number;
    corpId: string;
  }): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log('📋 处理Stream审批状态变更:', {
        processInstanceId: data.processInstanceId,
        result: data.result,
        type: data.type,
        staffId: data.staffId
      });

      // 获取审批实例详情
      const approvalDetail = await this.getApprovalDetail(data.processInstanceId);

      if (!approvalDetail) {
        console.warn('⚠️ 未找到审批实例:', data.processInstanceId);
        return {
          success: false,
          message: '审批实例不存在'
        };
      }

      // 更新本地审批实例状态
      const updateResult = await this.updateApprovalInstanceStatus(
        data.processInstanceId,
        data.result,
        data.finishTime
      );

      if (!updateResult.success) {
        console.error('❌ 更新审批实例状态失败:', updateResult.message);
        return updateResult;
      }

      // 如果是对公付款审批且审批通过，更新周预算已付金额
      if (data.result === 'agree' && approvalDetail.title?.includes('对公付款')) {
        const paymentResult = await this.processPaymentApproval(approvalDetail);

        if (!paymentResult.success) {
          console.warn('⚠️ 处理付款审批失败:', paymentResult.message);
          // 不影响主流程，只记录警告
        } else {
          console.log('✅ 付款审批处理成功:', paymentResult.data);
        }
      }

      return {
        success: true,
        message: 'Stream审批状态变更处理成功',
        data: {
          processInstanceId: data.processInstanceId,
          result: data.result,
          updated: true
        }
      };
    } catch (error) {
      console.error('❌ 处理Stream审批状态变更失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '处理审批状态变更失败'
      };
    }
  }

  /**
   * 获取审批详情
   */
  private async getApprovalDetail(processInstanceId: string): Promise<any> {
    try {
      const detail = await this.dingTalkService.getApprovalInstanceDetail(processInstanceId);
      return detail?.process_instance || null;
    } catch (error) {
      console.error('❌ 获取审批详情失败:', error);
      return null;
    }
  }

  /**
   * 更新审批实例状态
   */
  private async updateApprovalInstanceStatus(
    processInstanceId: string,
    result: string,
    finishTime?: number
  ): Promise<{ success: boolean; message: string }> {
    try {
      // 映射审批结果到系统状态
      let status: ApprovalStatus;
      switch (result) {
        case 'agree':
          status = ApprovalStatus.APPROVED;
          break;
        case 'refuse':
          status = ApprovalStatus.REJECTED;
          break;
        case 'redirect':
          status = ApprovalStatus.PENDING;
          break;
        default:
          status = ApprovalStatus.PENDING;
      }

      const updateData: any = {
        status,
        result,
        updatedAt: new Date()
      };

      if (finishTime) {
        updateData.finishTime = new Date(finishTime);
      }

      await this.databaseService.updateApprovalInstanceStatus({
        processInstanceId,
        status,
        result,
        finishTime: updateData.finishTime,
        actualAmount: updateData.actualAmount
      });

      console.log('✅ 审批实例状态更新成功:', {
        processInstanceId,
        status,
        result
      });

      return {
        success: true,
        message: '审批实例状态更新成功'
      };
    } catch (error) {
      console.error('❌ 更新审批实例状态失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '更新审批实例状态失败'
      };
    }
  }

  /**
   * 处理付款审批
   */
  private async processPaymentApproval(approvalDetail: any): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      // 从审批详情中提取付款信息
      const formValues = approvalDetail.form_component_values || [];
      const totalAmountField = formValues.find((field: any) =>
        field.name === APPROVAL_CONFIG.FORM_FIELDS.PAYMENT.TOTAL_AMOUNT
      );

      if (!totalAmountField || !totalAmountField.value) {
        return {
          success: false,
          message: '无法获取付款金额'
        };
      }

      const paymentAmount = Number(totalAmountField.value);

      // 这里可以添加更多的付款处理逻辑
      // 比如更新周预算、记录付款记录等

      return {
        success: true,
        message: '付款审批处理成功',
        data: {
          paymentAmount,
          processInstanceId: approvalDetail.process_instance_id
        }
      };
    } catch (error) {
      console.error('❌ 处理付款审批失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '处理付款审批失败'
      };
    }
  }
}
