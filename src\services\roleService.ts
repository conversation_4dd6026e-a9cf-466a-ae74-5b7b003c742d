import { Permission, Prisma, Role, RolePermission } from '@prisma/client';
import { DatabaseService } from './database.js';

export interface CreateRoleData {
  name: string;
  displayName: string;
  description?: string;
  isSystem?: boolean;
  isActive?: boolean;
  createdBy: string;
}

export interface UpdateRoleData {
  displayName?: string;
  description?: string;
  isActive?: boolean;
}

export interface RoleWithPermissions extends Role {
  rolePermissions: (RolePermission & {
    permission: Permission;
  })[];
}

export interface AssignPermissionData {
  roleId: string;
  permissionIds: string[];
  assignedBy: string;
}

export interface AssignUserRolesData {
  userid: string;
  roleIds: string[];
  assignedBy: string;
  expiresAt?: Date;
}

export class RoleService {
  constructor(private databaseService: DatabaseService) {}

  /**
   * 创建角色
   */
  async createRole(data: CreateRoleData): Promise<Role> {
    try {
      const role = await this.databaseService.client.role.create({
        data: {
          name: data.name,
          displayName: data.displayName,
          description: data.description,
          isSystem: data.isSystem || false,
          isActive: data.isActive !== false,
          createdBy: data.createdBy,
        },
      });

      console.log(`角色创建成功: ${role.name} (${role.id})`);
      return role;
    } catch (error) {
      console.error('创建角色失败:', error);
      throw error;
    }
  }

  /**
   * 更新角色
   */
  async updateRole(roleId: string, data: UpdateRoleData): Promise<Role> {
    try {
      // 检查角色是否存在
      const existingRole = await this.getRoleById(roleId);
      if (!existingRole) {
        throw new Error(`角色不存在: ${roleId}`);
      }

      // 系统角色不允许修改某些字段
      if (existingRole.isSystem && data.isActive === false) {
        throw new Error('系统角色不能被禁用');
      }

      const role = await this.databaseService.client.role.update({
        where: { id: roleId },
        data: {
          displayName: data.displayName,
          description: data.description,
          isActive: data.isActive,
          updatedAt: new Date(),
        },
      });

      console.log(`角色更新成功: ${role.name} (${role.id})`);
      return role;
    } catch (error) {
      console.error('更新角色失败:', error);
      throw error;
    }
  }

  /**
   * 删除角色
   */
  async deleteRole(roleId: string): Promise<void> {
    try {
      // 检查角色是否存在
      const existingRole = await this.getRoleById(roleId);
      if (!existingRole) {
        throw new Error(`角色不存在: ${roleId}`);
      }

      // 系统角色不允许删除
      if (existingRole.isSystem) {
        throw new Error('系统角色不能被删除');
      }

      // 检查是否有用户或部门正在使用此角色
      const userRoleCount = await this.databaseService.client.userRole.count({
        where: { roleId },
      });

      const departmentRoleCount = await this.databaseService.client.departmentRole.count({
        where: { roleId },
      });

      if (userRoleCount > 0 || departmentRoleCount > 0) {
        throw new Error('角色正在被使用，无法删除');
      }

      await this.databaseService.client.role.delete({
        where: { id: roleId },
      });

      console.log(`角色删除成功: ${existingRole.name} (${roleId})`);
    } catch (error) {
      console.error('删除角色失败:', error);
      throw error;
    }
  }

  /**
   * 获取角色详情
   */
  async getRoleById(roleId: string): Promise<Role | null> {
    try {
      return await this.databaseService.client.role.findUnique({
        where: { id: roleId },
      });
    } catch (error) {
      console.error('获取角色详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取角色详情（包含权限）
   */
  async getRoleWithPermissions(roleId: string): Promise<RoleWithPermissions | null> {
    try {
      return await this.databaseService.client.role.findUnique({
        where: { id: roleId },
        include: {
          rolePermissions: {
            include: {
              permission: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('获取角色详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取角色列表
   */
  async getRoles(options: {
    page?: number;
    pageSize?: number;
    isActive?: boolean;
    isSystem?: boolean;
    search?: string;
  } = {}): Promise<{
    roles: Role[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const {
        page = 1,
        pageSize = 20,
        isActive,
        isSystem,
        search,
      } = options;

      const where: Prisma.RoleWhereInput = {};

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      if (isSystem !== undefined) {
        where.isSystem = isSystem;
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { displayName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [roles, total] = await Promise.all([
        this.databaseService.client.role.findMany({
          where,
          orderBy: [
            { isSystem: 'desc' },
            { createdAt: 'desc' },
          ],
          skip: (page - 1) * pageSize,
          take: pageSize,
        }),
        this.databaseService.client.role.count({ where }),
      ]);

      return {
        roles,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('获取角色列表失败:', error);
      throw error;
    }
  }

  /**
   * 为角色分配权限
   */
  async assignPermissions(data: AssignPermissionData): Promise<void> {
    try {
      const { roleId, permissionIds, assignedBy } = data;

      // 检查角色是否存在
      const role = await this.getRoleById(roleId);
      if (!role) {
        throw new Error(`角色不存在: ${roleId}`);
      }

      // 检查权限是否存在
      const permissions = await this.databaseService.client.permission.findMany({
        where: { id: { in: permissionIds } },
      });

      if (permissions.length !== permissionIds.length) {
        throw new Error('部分权限不存在');
      }

      // 使用事务处理权限分配
      await this.databaseService.client.$transaction(async (tx) => {
        // 删除现有权限
        await tx.rolePermission.deleteMany({
          where: { roleId },
        });

        // 添加新权限
        if (permissionIds.length > 0) {
          await tx.rolePermission.createMany({
            data: permissionIds.map(permissionId => ({
              roleId,
              permissionId,
              createdBy: assignedBy,
            })),
          });
        }
      });

      console.log(`角色权限分配成功: ${role.name}, 权限数量: ${permissionIds.length}`);
    } catch (error) {
      console.error('分配角色权限失败:', error);
      throw error;
    }
  }

  /**
   * 获取角色的权限列表
   */
  async getRolePermissions(roleId: string): Promise<Permission[]> {
    try {
      const rolePermissions = await this.databaseService.client.rolePermission.findMany({
        where: { roleId },
        include: {
          permission: true,
        },
      });

      return rolePermissions.map(rp => rp.permission);
    } catch (error) {
      console.error('获取角色权限失败:', error);
      throw error;
    }
  }

  /**
   * 检查角色名称是否可用
   */
  async isRoleNameAvailable(name: string, excludeRoleId?: string): Promise<boolean> {
    try {
      const where: Prisma.RoleWhereInput = { name };
      
      if (excludeRoleId) {
        where.id = { not: excludeRoleId };
      }

      const existingRole = await this.databaseService.client.role.findFirst({
        where,
      });

      return !existingRole;
    } catch (error) {
      console.error('检查角色名称可用性失败:', error);
      throw error;
    }
  }

  async assignUserRoles(data: AssignUserRolesData): Promise<void> {
    try {
      const { userid, roleIds, assignedBy, expiresAt } = data;

      // 检查用户是否存在
      const user = await this.databaseService.client.user.findUnique({
        where: { userid },
      });

      if (!user) {
        throw new Error(`用户不存在: ${userid}`);
      }

      // 检查角色是否存在
      const roles = await this.databaseService.client.role.findMany({
        where: { id: { in: roleIds }, isActive: true },
      });

      if (roles.length !== roleIds.length) {
        throw new Error('部分角色不存在或已禁用');
      }

      // 使用事务处理角色分配
      await this.databaseService.client.$transaction(async (tx) => {
        // 删除现有角色
        await tx.userRole.deleteMany({
          where: { userid },
        });

        // 添加新角色
        if (roleIds.length > 0) {
          await tx.userRole.createMany({
            data: roleIds.map(roleId => ({
              userid,
              roleId,
              createdBy: assignedBy,
              expiresAt,
            })),
          });
        }
      });

      console.log(`用户角色分配成功: ${userid}, 角色数量: ${roleIds.length}`);
    } catch (error) {
      console.error('分配用户角色失败:', error);
      throw error;
    }
  }
}
