import { FastifyInstance } from 'fastify';
import { WeeklyBudgetController } from '../controllers/weeklyBudget.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';

export async function weeklyBudgetRoutes(fastify: FastifyInstance) {
  const weeklyBudgetController = new WeeklyBudgetController();

  // 周预算管理路由

  // 创建项目周预算
  fastify.post('/projects/:projectId/weekly-budgets', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '创建项目周预算',
      tags: ['WeeklyBudget'],
      params: {
        type: 'object',
        required: ['projectId'],
        properties: {
          projectId: { type: 'string', description: '项目ID' }
        }
      },
      body: {
        type: 'object',
        required: ['title', 'weekStartDate', 'weekEndDate', 'serviceType', 'serviceContent', 'contractAmount', 'taxRate'],
        properties: {
          title: { type: 'string', description: '预算标题' },
          weekStartDate: { type: 'string', format: 'date', description: '周开始日期' },
          weekEndDate: { type: 'string', format: 'date', description: '周结束日期' },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          serviceContent: { type: 'string', description: '服务内容描述' },
          remarks: { type: 'string', description: '备注' },
          contractAmount: { type: 'number', minimum: 0, description: '合同金额' },
          taxRate: {
            type: 'string',
            enum: ['special_1', 'special_3', 'special_6', 'general'],
            description: '税率'
          },
          supplierId: { type: 'string', description: '供应商ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, weeklyBudgetController.createWeeklyBudget.bind(weeklyBudgetController));

  // 批量创建项目周预算
  fastify.post('/projects/:projectId/weekly-budgets/batch', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '批量创建项目周预算',
      tags: ['WeeklyBudget'],
      params: {
        type: 'object',
        required: ['projectId'],
        properties: {
          projectId: { type: 'string', description: '项目ID' }
        }
      },
      body: {
        type: 'object',
        required: ['startDate', 'endDate', 'serviceType', 'defaultContractAmount', 'defaultTaxRate'],
        properties: {
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          defaultContractAmount: { type: 'number', minimum: 0, description: '默认合同金额' },
          defaultTaxRate: {
            type: 'string',
            enum: ['special_1', 'special_3', 'special_6', 'general'],
            description: '默认税率'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'array' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, weeklyBudgetController.batchCreateWeeklyBudgets.bind(weeklyBudgetController));

  // 获取周预算列表
  fastify.get('/weekly-budgets', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取周预算列表',
      tags: ['WeeklyBudget'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          projectId: { type: 'string', description: '项目ID' },
          supplierId: { type: 'string', description: '供应商ID' },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          status: {
            type: 'string',
            enum: ['created', 'approved', 'executing', 'completed', 'cancelled'],
            description: '预算状态'
          },
          year: { type: 'string', description: '年份' },
          weekNumber: { type: 'string', description: '周数' },
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          sortBy: {
            type: 'string',
            enum: ['weekStartDate', 'contractAmount', 'createdAt'],
            description: '排序字段'
          },
          sortOrder: {
            type: 'string',
            enum: ['asc', 'desc'],
            description: '排序方向'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                weeklyBudgets: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      title: { type: 'string' },
                      weekStartDate: { type: 'string' },
                      weekEndDate: { type: 'string' },
                      weekNumber: { type: 'number' },
                      year: { type: 'number' },
                      serviceType: { type: 'string' },
                      serviceContent: { type: 'string' },
                      remarks: { type: 'string' },
                      contractAmount: { type: 'number' },
                      taxRate: { type: 'string' },
                      paidAmount: { type: 'number' },
                      remainingAmount: { type: 'number' },
                      status: { type: 'string' },
                      projectId: { type: 'string' },
                      supplierId: { type: 'string' },
                      supplier: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          name: { type: 'string' },
                          shortName: { type: 'string' }
                        }
                      },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' },
                      createdBy: { type: 'string' },
                      updatedBy: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
                totalPages: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, weeklyBudgetController.getWeeklyBudgets.bind(weeklyBudgetController));

  // 获取单个周预算
  fastify.get('/weekly-budgets/:id', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取单个周预算',
      tags: ['WeeklyBudget'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '周预算ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, weeklyBudgetController.getWeeklyBudget.bind(weeklyBudgetController));

  // 更新周预算
  fastify.put('/weekly-budgets/:id', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '更新周预算',
      tags: ['WeeklyBudget'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '周预算ID' }
        }
      },
      body: {
        type: 'object',
        properties: {
          title: { type: 'string', description: '预算标题' },
          weekStartDate: { type: 'string', format: 'date', description: '周开始日期' },
          weekEndDate: { type: 'string', format: 'date', description: '周结束日期' },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          serviceContent: { type: 'string', description: '服务内容描述' },
          remarks: { type: 'string', description: '备注' },
          contractAmount: { type: 'number', minimum: 0, description: '合同金额' },
          taxRate: {
            type: 'string',
            enum: ['special_1', 'special_3', 'special_6', 'general'],
            description: '税率'
          },
          status: {
            type: 'string',
            enum: ['draft', 'approved', 'executing', 'completed', 'cancelled'],
            description: '预算状态'
          },
          paidAmount: { type: 'number', minimum: 0, description: '已付金额' },
          supplierId: { type: 'string', description: '供应商ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, weeklyBudgetController.updateWeeklyBudget.bind(weeklyBudgetController));

  // 删除周预算
  fastify.delete('/weekly-budgets/:id', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '删除周预算',
      tags: ['WeeklyBudget'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '周预算ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, weeklyBudgetController.deleteWeeklyBudget.bind(weeklyBudgetController));

  // 获取周预算统计
  fastify.get('/weekly-budgets/stats', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取周预算统计',
      tags: ['WeeklyBudget'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, weeklyBudgetController.getWeeklyBudgetStats.bind(weeklyBudgetController));

  // 发起周预算对公付款审批
  fastify.post('/weekly-budgets/approval', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '发起周预算对公付款审批',
      tags: ['WeeklyBudget'],
      body: {
        type: 'object',
        required: ['weeklyBudgetId', 'totalAmount', 'paymentReason', 'expectedPaymentDate', 'receivingAccount'],
        properties: {
          weeklyBudgetId: { type: 'string', description: '周预算ID' },
          totalAmount: { type: 'number', minimum: 0.01, description: '付款总额' },
          paymentReason: { type: 'string', description: '付款事由' },
          contractEntity: {
            type: 'string',
            enum: ['company_a', 'company_b', 'subsidiary', 'other'],
            description: '合同签署主体'
          },
          expectedPaymentDate: { type: 'string', format: 'date', description: '期望付款时间' },
          paymentMethod: {
            type: 'string',
            enum: ['bank_transfer', 'online_payment', 'check', 'cash', 'other'],
            description: '付款方式'
          },
          receivingAccount: {
            type: 'object',
            required: ['accountName', 'accountNumber', 'bankName'],
            properties: {
              accountName: { type: 'string', description: '账户名称' },
              accountNumber: { type: 'string', description: '账号' },
              bankName: { type: 'string', description: '开户银行' },
              bankCode: { type: 'string', description: '银行代码' }
            }
          },
          relatedApprovalId: { type: 'string', description: '关联审批单' },
          invoiceFiles: {
            type: 'array',
            items: { type: 'string' },
            description: '发票文件URL列表'
          },
          attachments: {
            type: 'array',
            items: { type: 'string' },
            description: '附件URL列表'
          },
          remark: { type: 'string', description: '备注' },
          // 兼容旧版本字段
          approvalAmount: { type: 'number', description: '审批金额（兼容）' },
          reason: { type: 'string', description: '原因（兼容）' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, weeklyBudgetController.createPaymentApproval.bind(weeklyBudgetController));

  console.log('📊 周预算管理路由已注册');
}
