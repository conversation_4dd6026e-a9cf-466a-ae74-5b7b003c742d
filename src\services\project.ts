import {
    <PERSON><PERSON><PERSON><PERSON>,
    Brand,
    BrandFinancialDetail,
    BrandFinancialSummary,
    BrandListResponse,
    BrandQueryParams,
    ContractSigningStatus,
    ContractType,
    CreateBrandRequest,
    CreateProjectRequest,
    CreateProjectRevenueRequest,
    CreateSupplierRequest,
    CreateWeeklyBudgetRequest,
    DocumentType,
    FileUploadResponse,
    FinancialReportQueryParams,
    FinancialReportResponse,
    Project,
    ProjectListResponse,
    ProjectProfit,
    ProjectQueryParams,
    ProjectRevenue,
    ProjectRevenueListResponse,
    ProjectRevenueQueryParams,
    ProjectStats,
    ProjectStatus,
    ProjectTemplate,
    RevenueStats,
    RevenueStatus,
    ServiceType,
    Supplier,
    SupplierListResponse,
    SupplierQueryParams,
    SupplierStats,
    TaxRate,
    UpdateBrandRequest,
    UpdateProjectRequest,
    UpdateProjectRevenueRequest,
    UpdateSupplierRequest,
    UpdateWeeklyBudgetRequest,
    User,
    WeeklyBudget,
    WeeklyBudgetListResponse,
    WeeklyBudgetQueryParams,
    WeeklyBudgetStats
} from '../types/project.js';
import { db } from './database.js';
import { DingTalkService } from './dingtalk.js';
import { DingTalkNotificationService } from './dingtalkNotification.js';
import { UserSyncService } from './userSync.js';

export class ProjectService {
  private dingTalkService: DingTalkService;
  private notificationService: DingTalkNotificationService;
  private userSyncService: UserSyncService;
  private useDatabase: boolean;

  // 模拟数据存储（当不使用数据库时）
  private projects: Map<string, Project> = new Map();
  private brands: Map<string, Brand> = new Map();
  private attachments: Map<string, Attachment> = new Map();
  private templates: Map<string, ProjectTemplate> = new Map();

  constructor() {
    this.dingTalkService = new DingTalkService();
    this.notificationService = new DingTalkNotificationService();
    this.userSyncService = new UserSyncService(db, this.dingTalkService);
    // 检查是否配置了数据库
    this.useDatabase = !!process.env.DATABASE_URL;

    if (!this.useDatabase) {
      console.log('🔄 使用内存存储模式');
      this.initializeMockData();
    } else {
      console.log('🗄️ 使用PostgreSQL数据库模式');
    }
  }

  // 初始化模拟数据
  private initializeMockData() {
    // 创建示例品牌
    const mockBrands: Brand[] = [
      {
        id: 'brand-001',
        name: '可口可乐',
        description: '全球知名饮料品牌',
        status: 'active',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        createdBy: 'admin'
      },
      {
        id: 'brand-002',
        name: '耐克',
        description: '运动品牌',
        status: 'active',
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02'),
        createdBy: 'admin'
      },
      {
        id: 'brand-003',
        name: '苹果',
        description: '科技品牌',
        status: 'active',
        createdAt: new Date('2024-01-03'),
        updatedAt: new Date('2024-01-03'),
        createdBy: 'admin'
      }
    ];

    mockBrands.forEach(brand => {
      this.brands.set(brand.id, brand);
    });

    // 创建示例项目
    const mockProjects: Project[] = [
      {
        id: 'project-001',
        documentType: DocumentType.PROJECT_INITIATION,
        brandId: 'brand-001',
        projectName: '可口可乐春节营销活动',
        period: {
          startDate: new Date('2024-02-01'),
          endDate: new Date('2024-02-29')
        },
        budget: {
          planningBudget: 1000000,
          influencerBudget: 400000,
          adBudget: 300000,
          otherBudget: 100000
        },
        cost: {
          influencerCost: 350000,
          adCost: 280000,
          otherCost: 80000,
          estimatedInfluencerRebate: 20000
        },
        profit: {
          profit: 0, // 将自动计算
          grossMargin: 0 // 将自动计算
        },
        executorPM: 'user-001',
        contentMediaIds: ['user-002', 'user-003'],
        contractType: ContractType.ANNUAL_FRAME,
        contractSigningStatus: ContractSigningStatus.SIGNED,
        settlementRules: '<p>按月结算，每月25日前提交结算单</p>',
        kpi: '<p>目标曝光量：1000万<br>目标转化率：3%</p>',
        attachments: [],
        status: 'completed', // 已完成项目
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-03-01'),
        createdBy: 'admin',
        updatedBy: 'admin'
      },
      {
        id: 'project-002',
        documentType: DocumentType.PROJECT_INITIATION,
        brandId: 'brand-002',
        projectName: '耐克夏季运动推广',
        period: {
          startDate: new Date('2024-06-01'),
          endDate: new Date('2024-08-31')
        },
        budget: {
          planningBudget: 800000,
          influencerBudget: 320000,
          adBudget: 240000,
          otherBudget: 80000
        },
        cost: {
          influencerCost: 300000,
          adCost: 220000,
          otherCost: 70000,
          estimatedInfluencerRebate: 15000
        },
        profit: {
          profit: 0, // 将自动计算
          grossMargin: 0 // 将自动计算
        },
        executorPM: 'user-002',
        contentMediaIds: ['user-003', 'user-004'],
        contractType: ContractType.QUARTERLY_FRAME,
        contractSigningStatus: ContractSigningStatus.SIGNING,
        settlementRules: '<p>按季度结算，每季度末结算</p>',
        kpi: '<p>目标销售额：500万<br>目标ROI：2.5</p>',
        attachments: [],
        status: 'active', // 进行中项目
        createdAt: new Date('2024-05-15'),
        updatedAt: new Date('2024-06-15'),
        createdBy: 'admin',
        updatedBy: 'admin'
      },
      {
        id: 'project-003',
        documentType: DocumentType.PROJECT_INITIATION,
        brandId: 'brand-003',
        projectName: '苹果新品发布会',
        period: {
          startDate: new Date('2024-09-01'),
          endDate: new Date('2024-09-30')
        },
        budget: {
          planningBudget: 1200000,
          influencerBudget: 480000,
          adBudget: 360000,
          otherBudget: 120000
        },
        cost: {
          influencerCost: 450000,
          adCost: 340000,
          otherCost: 110000,
          estimatedInfluencerRebate: 25000
        },
        profit: {
          profit: 0, // 将自动计算
          grossMargin: 0 // 将自动计算
        },
        executorPM: 'user-003',
        contentMediaIds: ['user-001', 'user-002'],
        contractType: ContractType.SINGLE,
        contractSigningStatus: ContractSigningStatus.NO_CONTRACT,
        settlementRules: '<p>项目完成后一次性结算</p>',
        kpi: '<p>目标观看量：2000万<br>目标互动率：5%</p>',
        attachments: [],
        status: 'active', // 进行中项目
        createdAt: new Date('2024-08-01'),
        updatedAt: new Date('2024-08-15'),
        createdBy: 'admin',
        updatedBy: 'admin'
      },
      {
        id: 'project-004',
        documentType: DocumentType.PROJECT_INITIATION,
        brandId: 'brand-001',
        projectName: '可口可乐夏日清爽活动',
        period: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-01-31')
        },
        budget: {
          planningBudget: 600000,
          influencerBudget: 240000,
          adBudget: 180000,
          otherBudget: 60000
        },
        cost: {
          influencerCost: 220000,
          adCost: 170000,
          otherCost: 55000,
          estimatedInfluencerRebate: 12000
        },
        profit: {
          profit: 0, // 将自动计算
          grossMargin: 0 // 将自动计算
        },
        executorPM: 'user-004',
        contentMediaIds: ['user-001', 'user-003'],
        contractType: ContractType.SINGLE,
        contractSigningStatus: ContractSigningStatus.PENDING,
        settlementRules: '<p>项目完成后结算</p>',
        kpi: '<p>目标销量：100万瓶<br>目标覆盖：500万人</p>',
        attachments: [],
        status: 'completed', // 已完成项目
        createdAt: new Date('2023-12-15'),
        updatedAt: new Date('2024-02-15'),
        createdBy: 'admin',
        updatedBy: 'admin'
      }
    ];

    mockProjects.forEach(project => {
      // 计算利润
      project.profit = this.calculateProfit(project.budget, project.cost);
      this.projects.set(project.id, project);
    });
  }

  // 计算项目利润
  // 注意：estimatedInfluencerRebate（达人返点）虽然在cost对象中，但实际上是收入性质
  // 利润计算公式：项目规划预算 - (达人成本 + 投流成本 + 其他成本) + 达人返点
  private calculateProfit(budget: Project['budget'], cost: Project['cost']): ProjectProfit {
    const totalCost = cost.influencerCost + cost.adCost + cost.otherCost;
    const profit = budget.planningBudget - totalCost + cost.estimatedInfluencerRebate;
    const grossMargin = budget.planningBudget > 0 ? (profit / budget.planningBudget) * 100 : 0;

    return {
      profit: Math.round(profit * 100) / 100,
      grossMargin: Math.round(grossMargin * 100) / 100
    };
  }

  // 生成唯一ID
  private generateId(prefix: string): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 创建变更记录的辅助方法
  private async createChangeLog(data: {
    changeType: string;
    changeTitle: string;
    changeDetails?: any;
    beforeData?: any;
    afterData?: any;
    changedFields: string[];
    operatorId: string;
    operatorName: string;
    operatorIP?: string;
    userAgent?: string;
    reason?: string;
    description?: string;
    projectId: string;
  }) {
    if (this.useDatabase) {
      try {
        await db.createProjectChangeLog(data);
      } catch (error) {
        console.error('创建变更记录失败:', error);
      }
    }
    // 如果不使用数据库，暂时不记录变更日志
  }

  // 比较对象差异，获取变更字段
  // 核心业务逻辑：智能字段对比，只记录实际发生变化的字段
  // 解决问题：避免记录"伪变更"（如字段值从1更新为1的情况）
  private getChangedFields(before: any, after: any, updateRequest?: any): string[] {
    const changedFields: string[] = [];

    // 排除不应该被记录为变更的字段（系统自动维护的字段和关联信息字段）
    const excludeFields = new Set([
      'id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy',
      // 排除关联信息字段（这些是数据库查询时自动关联的，不是用户更新的）
      'brand', 'executorPMInfo', 'contentMediaInfo', 'attachments'
    ]);

    // 如果有更新请求，只对比更新请求中包含的字段
    // 这样可以避免对比数据库关联查询出来的额外字段
    const keysToCompare = updateRequest
      ? new Set(Object.keys(updateRequest).filter(key => !excludeFields.has(key)))
      : new Set([...Object.keys(before || {}), ...Object.keys(after || {})].filter(key => !excludeFields.has(key)));

    for (const key of keysToCompare) {
      // 跳过排除的字段
      if (excludeFields.has(key)) {
        continue;
      }

      // 比较字段值是否发生变更
      const beforeValue = before?.[key];
      const afterValue = after?.[key];

      // 使用深度比较来检测变更
      // 重要：只有当JSON序列化后的值不同时，才认为字段发生了变更
      // 这确保了数值1->1、字符串"test"->"test"等情况不会被误判为变更
      if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        changedFields.push(key);
      }
    }

    return changedFields;
  }

  // 获取用户信息用于变更记录
  private async getUserInfoForChangeLog(userid: string): Promise<{ userid: string; name: string }> {
    try {
      const userInfo = await this.getUserInfo(userid);
      return {
        userid: userInfo.userid,
        name: userInfo.name
      };
    } catch (error) {
      return {
        userid,
        name: `用户${userid}`
      };
    }
  }

  // 转换数据库Brand对象为接口Brand
  private transformDbBrand(dbBrand: any): Brand {
    return {
      id: dbBrand.id,
      name: dbBrand.name,
      description: dbBrand.description || undefined,
      logo: dbBrand.logo || undefined,
      status: dbBrand.status.toLowerCase(),
      createdAt: dbBrand.createdAt,
      updatedAt: dbBrand.updatedAt,
      createdBy: dbBrand.createdBy
    };
  }

  // 项目管理方法

  // 创建项目
  async createProject(request: CreateProjectRequest, createdBy: string, operatorInfo?: { ip?: string; userAgent?: string }): Promise<Project> {
    if (this.useDatabase) {
      const project = await db.createProject(request, createdBy);

      // 记录创建变更日志
      const userInfo = await this.getUserInfoForChangeLog(createdBy);
      // 对于创建操作，记录所有非空/非默认值的字段
      const createdFields = Object.keys(request).filter(key => {
        const value = (request as any)[key];
        return value !== undefined && value !== null && value !== '';
      });

      await this.createChangeLog({
        changeType: 'CREATE',
        changeTitle: `创建项目: ${request.projectName}`,
        changeDetails: {
          action: 'create_project',
          projectData: request
        },
        afterData: project,
        changedFields: createdFields,
        operatorId: createdBy,
        operatorName: userInfo.name,
        operatorIP: operatorInfo?.ip,
        userAgent: operatorInfo?.userAgent,
        description: `创建了新项目"${request.projectName}"，设置了${createdFields.length}个字段`,
        projectId: project.id
      });

      // 发送项目创建通知给执行PM
      await this.sendProjectCreatedNotification(project, createdBy);

      return project;
    }

    const id = this.generateId('project');

    // 验证品牌是否存在
    const brand = this.brands.get(request.brandId);
    if (!brand) {
      throw new Error('品牌不存在');
    }

    const now = new Date();
    const profit = this.calculateProfit(request.budget, request.cost);

    const project: Project = {
      id,
      ...request,
      contractSigningStatus: request.contractSigningStatus || ContractSigningStatus.PENDING,
      profit,
      attachments: [],
      status: 'draft',
      createdAt: now,
      updatedAt: now,
      createdBy,
      updatedBy: createdBy
    };

    this.projects.set(id, project);

    // 记录创建变更日志（内存模式下暂不记录）

    // 发送项目创建通知给执行PM（内存模式下也发送）
    this.sendProjectCreatedNotification(project, createdBy).catch(error => {
      console.error('发送项目创建通知失败:', error);
    });

    return project;
  }

  // 获取项目列表
  async getProjects(params: ProjectQueryParams = {}): Promise<ProjectListResponse> {
    if (this.useDatabase) {
      const res = await db.getProjects(params);
      console.log('从数据库获取项目列表:', res);
      return res;
    }

    const {
      page = 1,
      pageSize = 20,
      documentType,
      brandId,
      contractType,
      contractSigningStatus,
      executorPM,
      status,
      keyword,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = params;

    let projects = Array.from(this.projects.values());

    // 应用过滤器
    if (documentType) {
      projects = projects.filter(p => p.documentType === documentType);
    }
    if (brandId) {
      projects = projects.filter(p => p.brandId === brandId);
    }
    if (contractType) {
      projects = projects.filter(p => p.contractType === contractType);
    }
    if (contractSigningStatus) {
      projects = projects.filter(p => p.contractSigningStatus === contractSigningStatus);
    }
    if (executorPM) {
      projects = projects.filter(p => p.executorPM === executorPM);
    }
    if (status) {
      projects = projects.filter(p => p.status === status);
    }
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      projects = projects.filter(p =>
        p.projectName.toLowerCase().includes(lowerKeyword)
      );
    }
    if (startDate) {
      projects = projects.filter(p => p.period.startDate >= startDate);
    }
    if (endDate) {
      projects = projects.filter(p => p.period.endDate <= endDate);
    }

    // 排序
    projects.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'projectName':
          aValue = a.projectName;
          bValue = b.projectName;
          break;
        case 'profit':
          aValue = a.profit.profit;
          bValue = b.profit.profit;
          break;
        case 'updatedAt':
          aValue = a.updatedAt;
          bValue = b.updatedAt;
          break;
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // 分页
    const total = projects.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedProjects = projects.slice(startIndex, endIndex);

    // 填充关联数据
    for (const project of paginatedProjects) {
      project.brand = this.brands.get(project.brandId);

      // 获取用户信息（模拟）
      try {
        project.executorPMInfo = await this.getUserInfo(project.executorPM);
        project.contentMediaInfo = await Promise.all(
          project.contentMediaIds.map(id => this.getUserInfo(id))
        );
      } catch (error) {
        console.warn('获取用户信息失败:', error);
      }
    }

    return {
      projects: paginatedProjects,
      total,
      page,
      pageSize,
      totalPages
    };
  }

  // 获取单个项目
  async getProject(id: string): Promise<Project | null> {
    if (this.useDatabase) {
      const project = await db.getProject(id);
      if (project) {
        // 填充用户信息
        try {
          project.executorPMInfo = await this.getUserInfo(project.executorPM);
          project.contentMediaInfo = await Promise.all(
            project.contentMediaIds.map(id => this.getUserInfo(id))
          );
        } catch (error) {
          console.warn('获取用户信息失败:', error);
        }
      }
      return project;
    }

    const project = this.projects.get(id);
    if (!project) {
      return null;
    }

    // 填充关联数据
    project.brand = this.brands.get(project.brandId);

    try {
      project.executorPMInfo = await this.getUserInfo(project.executorPM);
      project.contentMediaInfo = await Promise.all(
        project.contentMediaIds.map(id => this.getUserInfo(id))
      );
    } catch (error) {
      console.warn('获取用户信息失败:', error);
    }

    return project;
  }

  // 更新项目
  async updateProject(request: UpdateProjectRequest, updatedBy: string, operatorInfo?: { ip?: string; userAgent?: string }): Promise<Project> {
    if (this.useDatabase) {
      // 获取更新前的项目数据
      const beforeProject = await db.getProject(request.id);
      if (!beforeProject) {
        throw new Error('项目不存在');
      }

      const updatedProject = await db.updateProject(request, updatedBy);

      // 记录更新变更日志
      const userInfo = await this.getUserInfoForChangeLog(updatedBy);
      // 传递更新请求给字段对比方法，确保只对比用户实际提交的字段
      const changedFields = this.getChangedFields(beforeProject, updatedProject, request);

      if (changedFields.length > 0) {
        await this.createChangeLog({
          changeType: 'UPDATE',
          changeTitle: `更新项目: ${updatedProject.projectName}`,
          changeDetails: {
            action: 'update_project',
            updateData: request
          },
          beforeData: beforeProject,
          afterData: updatedProject,
          changedFields,
          operatorId: updatedBy,
          operatorName: userInfo.name,
          operatorIP: operatorInfo?.ip,
          userAgent: operatorInfo?.userAgent,
          description: `更新了项目"${updatedProject.projectName}"的${changedFields.join(', ')}字段`,
          projectId: request.id
        });
      }

      return updatedProject;
    }

    const project = this.projects.get(request.id);
    if (!project) {
      throw new Error('项目不存在');
    }

    // 验证品牌是否存在（如果更新了品牌）
    if (request.brandId && request.brandId !== project.brandId) {
      const brand = this.brands.get(request.brandId);
      if (!brand) {
        throw new Error('品牌不存在');
      }
    }

    // 更新项目数据
    const updatedProject: Project = {
      ...project,
      ...request,
      updatedAt: new Date(),
      updatedBy
    };

    // 重新计算利润（如果预算或成本发生变化）
    if (request.budget || request.cost) {
      updatedProject.profit = this.calculateProfit(
        updatedProject.budget,
        updatedProject.cost
      );
    }

    this.projects.set(request.id, updatedProject);
    return updatedProject;
  }

  // 删除项目
  async deleteProject(id: string, deletedBy: string, operatorInfo?: { ip?: string; userAgent?: string }): Promise<boolean> {
    if (this.useDatabase) {
      // 获取删除前的项目数据
      const beforeProject = await db.getProject(id);
      if (!beforeProject) {
        throw new Error('项目不存在');
      }

      // 记录删除变更日志
      const userInfo = await this.getUserInfoForChangeLog(deletedBy);
      await this.createChangeLog({
        changeType: 'DELETE',
        changeTitle: `删除项目: ${beforeProject.projectName}`,
        changeDetails: {
          action: 'delete_project'
        },
        beforeData: beforeProject,
        changedFields: [], // 删除操作不涉及字段变更，使用空数组
        operatorId: deletedBy,
        operatorName: userInfo.name,
        operatorIP: operatorInfo?.ip,
        userAgent: operatorInfo?.userAgent,
        description: `删除了项目"${beforeProject.projectName}"`,
        projectId: id
      });

      return await db.deleteProject(id);
    }
    return this.projects.delete(id);
  }

  // 项目变更记录管理方法

  // 获取项目变更记录列表
  async getProjectChangeLogs(params: any = {}): Promise<any> {
    if (this.useDatabase) {
      return await db.getProjectChangeLogs(params);
    }

    // 内存模式下返回空列表
    return {
      changeLogs: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  // 获取单个项目的变更记录
  async getProjectChangeLogsByProjectId(projectId: string, params?: {
    page?: number;
    pageSize?: number;
    sortOrder?: string;
  }): Promise<any> {
    if (this.useDatabase) {
      return await db.getProjectChangeLogsByProjectId(projectId, params);
    }

    // 内存模式下返回空列表
    return {
      changeLogs: [],
      total: 0,
      page: params?.page || 1,
      pageSize: params?.pageSize || 50,
      totalPages: 0
    };
  }

  // 品牌管理方法

  // 创建品牌
  async createBrand(request: CreateBrandRequest, createdBy: string): Promise<Brand> {
    if (this.useDatabase) {
      const dbBrand = await db.createBrand(request, createdBy);
      return this.transformDbBrand(dbBrand);
    }

    const id = this.generateId('brand');
    const now = new Date();

    const brand: Brand = {
      id,
      ...request,
      status: 'active',
      createdAt: now,
      updatedAt: now,
      createdBy
    };

    this.brands.set(id, brand);
    return brand;
  }

  // 获取品牌列表
  async getBrands(params: BrandQueryParams = {}): Promise<BrandListResponse> {
    if (this.useDatabase) {
      const dbResult = await db.getBrands(params);
      return {
        ...dbResult,
        brands: dbResult.brands.map(brand => this.transformDbBrand(brand))
      };
    }

    const {
      page = 1,
      pageSize = 50,
      status,
      keyword,
      sortBy = 'name',
      sortOrder = 'asc'
    } = params;

    let brands = Array.from(this.brands.values());

    // 应用过滤器
    if (status) {
      brands = brands.filter(b => b.status === status);
    }
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      brands = brands.filter(b =>
        b.name.toLowerCase().includes(lowerKeyword) ||
        (b.description && b.description.toLowerCase().includes(lowerKeyword))
      );
    }

    // 排序
    brands.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'updatedAt':
          aValue = a.updatedAt;
          bValue = b.updatedAt;
          break;
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // 分页
    const total = brands.length;
    const totalPages = Math.ceil(total / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedBrands = brands.slice(startIndex, endIndex);

    return {
      brands: paginatedBrands,
      total,
      page,
      pageSize,
      totalPages
    };
  }

  // 获取单个品牌
  async getBrand(id: string): Promise<Brand | null> {
    if (this.useDatabase) {
      const dbBrand = await db.getBrand(id);
      return dbBrand ? this.transformDbBrand(dbBrand) : null;
    }
    return this.brands.get(id) || null;
  }

  // 更新品牌
  async updateBrand(request: UpdateBrandRequest): Promise<Brand> {
    if (this.useDatabase) {
      const dbBrand = await db.updateBrand(request);
      return this.transformDbBrand(dbBrand);
    }

    const brand = this.brands.get(request.id);
    if (!brand) {
      throw new Error('品牌不存在');
    }

    const updatedBrand: Brand = {
      ...brand,
      ...request,
      updatedAt: new Date()
    };

    this.brands.set(request.id, updatedBrand);
    return updatedBrand;
  }

  // 删除品牌
  async deleteBrand(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteBrand(id);
    }

    // 检查是否有项目使用此品牌
    const projectsUsingBrand = Array.from(this.projects.values())
      .filter(p => p.brandId === id);

    if (projectsUsingBrand.length > 0) {
      throw new Error('无法删除品牌，存在关联的项目');
    }

    return this.brands.delete(id);
  }

  // 获取项目统计信息
  async getProjectStats(): Promise<ProjectStats> {
    if (this.useDatabase) {
      return await db.getProjectStats();
    }

    const projects = Array.from(this.projects.values());

    const totalProjects = projects.length;
    const activeProjects = projects.filter(p => p.status === 'active').length;
    const completedProjects = projects.filter(p => p.status === 'completed').length;

    const totalBudget = projects.reduce((sum, p) => sum + p.budget.planningBudget, 0);
    const totalProfit = projects.reduce((sum, p) => sum + p.profit.profit, 0);
    const averageGrossMargin = projects.length > 0
      ? projects.reduce((sum, p) => sum + p.profit.grossMargin, 0) / projects.length
      : 0;

    // 按品牌统计
    const brandStats = new Map<string, { count: number; totalBudget: number; brandName: string }>();
    projects.forEach(p => {
      const brand = this.brands.get(p.brandId);
      const brandName = brand?.name || '未知品牌';
      const existing = brandStats.get(p.brandId) || { count: 0, totalBudget: 0, brandName };
      existing.count++;
      existing.totalBudget += p.budget.planningBudget;
      brandStats.set(p.brandId, existing);
    });

    // 按合同类型统计
    const contractStats = new Map<string, { count: number; totalBudget: number }>();
    projects.forEach(p => {
      const existing = contractStats.get(p.contractType) || { count: 0, totalBudget: 0 };
      existing.count++;
      existing.totalBudget += p.budget.planningBudget;
      contractStats.set(p.contractType, existing);
    });

    // 获取其他统计数据
    const revenueStats = await this.getRevenueStats();
    const weeklyBudgetStats = await this.getWeeklyBudgetStats();
    const supplierStats = await this.getSupplierStats();

    return {
      totalProjects,
      activeProjects,
      completedProjects,
      totalBudget,
      totalProfit,
      averageGrossMargin: Math.round(averageGrossMargin * 100) / 100,
      revenueStats,
      weeklyBudgetStats,
      supplierStats,
      projectsByBrand: Array.from(brandStats.entries()).map(([brandId, stats]) => ({
        brandId,
        brandName: stats.brandName,
        count: stats.count,
        totalBudget: stats.totalBudget
      })),
      projectsByContractType: Array.from(contractStats.entries()).map(([contractType, stats]) => ({
        contractType: contractType as any,
        count: stats.count,
        totalBudget: stats.totalBudget
      }))
    };
  }

  // 获取用户信息（通过钉钉API）
  private async getUserInfo(userid: string): Promise<User> {
    try {
      // 尝试通过钉钉API获取用户信息
      // 暂时使用模拟数据，实际应该调用钉钉API
      return {
        userid,
        name: `用户${userid}`,
        avatar: '',
        department: '营销部'
      };
    } catch (error) {
      console.warn(`获取用户信息失败: ${userid}`, error);
      return {
        userid,
        name: `用户${userid}`,
        department: '未知部门'
      };
    }
  }

  // 项目文件上传处理 - 写入数据库
  async uploadProjectFile(file: any, uploadedBy: string, projectId?: string): Promise<FileUploadResponse & { id: string; uploadedAt: Date; uploadedBy: string }> {
    const fs = await import('fs');
    const path = await import('path');
    const { pipeline } = await import('stream/promises');

    try {
      // 生成唯一文件名
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const fileExtension = path.extname(file.filename);
      const filename = `${timestamp}_${randomStr}${fileExtension}`;

      // 确保上传目录存在
      const uploadDir = path.resolve(process.cwd(), 'uploads');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // 保存文件到磁盘
      const filePath = path.join(uploadDir, filename);
      const writeStream = fs.createWriteStream(filePath);
      await pipeline(file.file, writeStream);

      // 获取文件大小
      const stats = fs.statSync(filePath);
      const fileSize = stats.size;

      // 构建文件URL
      const fileUrl = `/uploads/${filename}`;

      if (this.useDatabase && projectId) {
        // 写入数据库
        const attachment = await db.createAttachment({
          filename,
          originalName: file.filename,
          size: fileSize,
          mimeType: file.mimetype || 'application/octet-stream',
          url: fileUrl,
          projectId,
          uploadedBy
        });

        return {
          id: attachment.id,
          filename: attachment.filename,
          originalName: attachment.originalName,
          size: Number(attachment.size),
          mimeType: attachment.mimeType,
          url: attachment.url,
          uploadedAt: attachment.uploadedAt,
          uploadedBy: attachment.uploadedBy
        };
      } else {
        // 内存模式或临时上传（没有projectId）
        const id = this.generateId('file');
        const attachment: Attachment = {
          id,
          filename,
          originalName: file.filename,
          size: fileSize,
          mimeType: file.mimetype || 'application/octet-stream',
          url: fileUrl,
          uploadedAt: new Date(),
          uploadedBy
        };

        this.attachments.set(id, attachment);

        return {
          id,
          filename,
          originalName: file.filename,
          size: fileSize,
          mimeType: file.mimetype || 'application/octet-stream',
          url: fileUrl,
          uploadedAt: attachment.uploadedAt,
          uploadedBy
        };
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      throw new Error('文件上传失败');
    }
  }

  // 文件上传处理 (兼容旧接口)
  async uploadFile(file: any, uploadedBy: string): Promise<FileUploadResponse> {
    const result = await this.uploadProjectFile(file, uploadedBy);
    return {
      id: result.id,
      filename: result.filename,
      originalName: result.originalName,
      size: result.size,
      mimeType: result.mimeType,
      url: result.url
    };
  }

  // 获取附件信息
  async getAttachment(id: string): Promise<Attachment | null> {
    return this.attachments.get(id) || null;
  }

  // 项目收入管理方法

  // 创建项目收入
  async createProjectRevenue(projectId: string, request: CreateProjectRevenueRequest, createdBy: string): Promise<ProjectRevenue> {
    if (this.useDatabase) {
      return await db.createProjectRevenue(projectId, request, createdBy);
    }

    // 内存模式实现
    const id = this.generateId('revenue');
    const now = new Date();

    const revenue: ProjectRevenue = {
      id,
      title: request.title,
      revenueType: request.revenueType,
      status: 'receiving' as RevenueStatus,
      plannedAmount: request.plannedAmount,
      plannedDate: request.plannedDate ? new Date(request.plannedDate) : new Date(),
      milestone: request.milestone,
      paymentTerms: request.paymentTerms,
      notes: request.notes,
      projectId,
      createdAt: now,
      updatedAt: now,
      createdBy,
      updatedBy: createdBy
    };

    // 这里应该有一个收入存储，暂时返回创建的对象
    return revenue;
  }

  // 获取项目收入列表
  async getProjectRevenues(params: ProjectRevenueQueryParams = {}): Promise<ProjectRevenueListResponse> {
    if (this.useDatabase) {
      return await db.getProjectRevenues(params);
    }

    // 内存模式实现 - 返回空列表
    return {
      revenues: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  // 获取单个项目收入
  async getProjectRevenue(id: string): Promise<ProjectRevenue | null> {
    if (this.useDatabase) {
      return await db.getProjectRevenue(id);
    }

    // 内存模式实现 - 返回null
    return null;
  }

  // 更新项目收入
  async updateProjectRevenue(request: UpdateProjectRevenueRequest, updatedBy: string): Promise<ProjectRevenue> {
    if (this.useDatabase) {
      return await db.updateProjectRevenue(request, updatedBy);
    }

    // 内存模式实现 - 抛出错误
    throw new Error('项目收入不存在');
  }

  // 删除项目收入
  async deleteProjectRevenue(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteProjectRevenue(id);
    }

    // 内存模式实现 - 返回false
    return false;
  }

  // 获取收入统计
  async getRevenueStats(): Promise<RevenueStats> {
    if (this.useDatabase) {
      return await db.getRevenueStats();
    }

    // 内存模式实现 - 返回空统计
    return {
      totalPlannedRevenue: 0,
      totalActualRevenue: 0,
      totalInvoicedRevenue: 0,
      totalReceivedRevenue: 0,
      revenueByStatus: [],
      revenueByType: [],
      monthlyRevenueTrend: []
    };
  }

  // 确认项目收入
  async confirmProjectRevenue(id: string, request: any, updatedBy: string): Promise<ProjectRevenue> {
    if (this.useDatabase) {
      return await db.confirmProjectRevenue(id, request, updatedBy);
    }

    // 内存模式实现 - 抛出错误
    throw new Error('项目收入不存在');
  }

  // 批量确认项目收入
  async batchConfirmProjectRevenues(revenues: Array<{
    id: string;
    actualAmount: number;
    confirmedDate?: string;
    notes?: string;
  }>, updatedBy: string): Promise<any> {
    if (this.useDatabase) {
      return await db.batchConfirmProjectRevenues(revenues, updatedBy);
    }

    // 内存模式实现 - 返回空结果
    return {
      successCount: 0,
      failureCount: revenues.length,
      results: revenues.map(r => ({
        id: r.id,
        success: false,
        error: '内存模式不支持批量确认收入'
      }))
    };
  }

  // 供应商管理方法

  // 创建供应商
  async createSupplier(request: CreateSupplierRequest, createdBy: string): Promise<Supplier> {
    if (this.useDatabase) {
      return await db.createSupplier(request, createdBy);
    }

    // 内存模式实现
    const id = this.generateId('supplier');
    const now = new Date();

    const supplier: Supplier = {
      id,
      name: request.name,
      shortName: request.shortName,
      code: request.code,
      contactPerson: request.contactPerson,
      contactPhone: request.contactPhone,
      contactEmail: request.contactEmail,
      address: request.address,
      taxNumber: request.taxNumber,
      bankAccount: request.bankAccount,
      bankName: request.bankName,
      legalPerson: request.legalPerson,
      serviceTypes: request.serviceTypes,
      preferredTaxRate: request.preferredTaxRate,
      creditLimit: request.creditLimit,
      paymentTerms: request.paymentTerms,
      status: 'active' as any,
      rating: request.rating,
      notes: request.notes,
      createdAt: now,
      updatedAt: now,
      createdBy,
      updatedBy: createdBy
    };

    return supplier;
  }

  // 获取供应商列表
  async getSuppliers(params: SupplierQueryParams = {}): Promise<SupplierListResponse> {
    if (this.useDatabase) {
      return await db.getSuppliers(params);
    }

    // 内存模式实现 - 返回空列表
    return {
      suppliers: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  // 获取单个供应商
  async getSupplier(id: string): Promise<Supplier | null> {
    if (this.useDatabase) {
      return await db.getSupplier(id);
    }

    // 内存模式实现 - 返回null
    return null;
  }

  // 更新供应商
  async updateSupplier(request: UpdateSupplierRequest, updatedBy: string): Promise<Supplier> {
    if (this.useDatabase) {
      return await db.updateSupplier(request, updatedBy);
    }

    // 内存模式实现 - 抛出错误
    throw new Error('供应商不存在');
  }

  // 删除供应商
  async deleteSupplier(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteSupplier(id);
    }

    // 内存模式实现 - 返回false
    return false;
  }

  // 获取供应商统计
  async getSupplierStats(): Promise<SupplierStats> {
    if (this.useDatabase) {
      return await db.getSupplierStats();
    }

    // 内存模式实现 - 返回空统计
    return {
      totalSuppliers: 0,
      activeSuppliers: 0,
      suppliersByServiceType: [],
      suppliersByRating: []
    };
  }

  // 周预算管理方法

  // 创建周预算
  async createWeeklyBudget(projectId: string, request: CreateWeeklyBudgetRequest, createdBy: string): Promise<WeeklyBudget> {
    if (this.useDatabase) {
      const weeklyBudget = await db.createWeeklyBudget(projectId, request, createdBy);

      // 异步检查周预算是否超过项目成本10%
      this.checkWeeklyBudgetExceeded(weeklyBudget, projectId).catch(error => {
        console.error('检查周预算超额失败:', error);
      });

      return weeklyBudget;
    }

    // 内存模式实现
    const id = this.generateId('weekly-budget');
    const now = new Date();
    const startDate = new Date(request.weekStartDate);
    const endDate = new Date(request.weekEndDate);

    // 计算周数和年份
    const year = startDate.getFullYear();
    const weekNumber = this.getWeekNumber(startDate);

    const weeklyBudget: WeeklyBudget = {
      id,
      title: request.title,
      weekStartDate: startDate,
      weekEndDate: endDate,
      weekNumber,
      year,
      serviceType: request.serviceType,
      serviceContent: request.serviceContent,
      remarks: request.remarks,
      contractAmount: request.contractAmount,
      taxRate: request.taxRate,
      paidAmount: 0,
      remainingAmount: request.contractAmount,
      status: 'draft' as any,
      approvalStatus: 'none' as any,
      projectId,
      supplierId: request.supplierId,
      createdAt: now,
      updatedAt: now,
      createdBy,
      updatedBy: createdBy
    };

    // 异步检查周预算是否超过项目成本10%（内存模式下也检查）
    this.checkWeeklyBudgetExceeded(weeklyBudget, projectId).catch(error => {
      console.error('检查周预算超额失败:', error);
    });

    return weeklyBudget;
  }

  // 获取周预算列表
  async getWeeklyBudgets(params: WeeklyBudgetQueryParams = {}): Promise<WeeklyBudgetListResponse> {
    if (this.useDatabase) {
      return await db.getWeeklyBudgets(params);
    }

    // 内存模式实现 - 返回空列表
    return {
      weeklyBudgets: [],
      total: 0,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      totalPages: 0
    };
  }

  // 获取单个周预算
  async getWeeklyBudget(id: string): Promise<WeeklyBudget | null> {
    if (this.useDatabase) {
      return await db.getWeeklyBudget(id);
    }

    // 内存模式实现 - 返回null
    return null;
  }

  // 更新周预算
  async updateWeeklyBudget(request: UpdateWeeklyBudgetRequest, updatedBy: string): Promise<WeeklyBudget> {
    if (this.useDatabase) {
      const weeklyBudget = await db.updateWeeklyBudget(request, updatedBy);

      // 如果更新了合同金额，检查是否超过项目成本10%
      if (request.contractAmount !== undefined) {
        this.checkWeeklyBudgetExceeded(weeklyBudget, weeklyBudget.projectId).catch(error => {
          console.error('检查周预算超额失败:', error);
        });
      }

      return weeklyBudget;
    }

    // 内存模式实现 - 抛出错误
    throw new Error('周预算不存在');
  }

  // 删除周预算
  async deleteWeeklyBudget(id: string): Promise<boolean> {
    if (this.useDatabase) {
      return await db.deleteWeeklyBudget(id);
    }

    // 内存模式实现 - 返回false
    return false;
  }

  // 获取周预算统计
  async getWeeklyBudgetStats(): Promise<WeeklyBudgetStats> {
    if (this.useDatabase) {
      return await db.getWeeklyBudgetStats();
    }

    // 内存模式实现 - 返回空统计
    return {
      totalBudgets: 0,
      totalContractAmount: 0,
      totalPaidAmount: 0,
      totalRemainingAmount: 0,
      budgetsByServiceType: [],
      budgetsByStatus: [],
      budgetsBySupplier: [],
      weeklyTrend: []
    };
  }

  // 批量创建周预算
  async batchCreateWeeklyBudgets(
    projectId: string,
    startDate: string,
    endDate: string,
    serviceType: ServiceType,
    defaultContractAmount: number,
    defaultTaxRate: TaxRate,
    createdBy: string
  ): Promise<WeeklyBudget[]> {
    if (this.useDatabase) {
      const weeklyBudgets = await db.batchCreateWeeklyBudgets(projectId, startDate, endDate, serviceType, defaultContractAmount, defaultTaxRate, createdBy);

      // 异步检查每个周预算是否超过项目成本10%
      weeklyBudgets.forEach(weeklyBudget => {
        this.checkWeeklyBudgetExceeded(weeklyBudget, projectId).catch(error => {
          console.error('检查周预算超额失败:', error);
        });
      });

      return weeklyBudgets;
    }

    // 内存模式实现 - 返回空数组
    return [];
  }

  // 辅助方法：获取周数
  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  // 财务报表方法

  // 获取品牌财务汇总报表
  async getBrandFinancialSummary(params: FinancialReportQueryParams = {}): Promise<FinancialReportResponse> {
    if (this.useDatabase) {
      return await db.getBrandFinancialSummary(params);
    }

    // 内存模式实现
    const brands = Array.from(this.brands.values());
    const projects = Array.from(this.projects.values());

    // 应用过滤器
    let filteredProjects = projects;
    if (params.brandId) {
      filteredProjects = filteredProjects.filter(p => p.brandId === params.brandId);
    }
    if (params.startDate) {
      const startDate = new Date(params.startDate);
      filteredProjects = filteredProjects.filter(p => p.period.startDate >= startDate);
    }
    if (params.endDate) {
      const endDate = new Date(params.endDate);
      filteredProjects = filteredProjects.filter(p => p.period.endDate <= endDate);
    }
    if (params.projectStatus) {
      filteredProjects = filteredProjects.filter(p => params.projectStatus!.includes(p.status as ProjectStatus));
    }

    // 按品牌分组计算财务数据
    const brandSummaries: BrandFinancialSummary[] = [];
    let totalOrderAmount = 0;
    let totalExecutedAmount = 0;
    let totalEstimatedProfit = 0;
    let totalReceivedAmount = 0;
    let totalPaidAmount = 0;

    for (const brand of brands) {
      const brandProjects = filteredProjects.filter(p => p.brandId === brand.id);

      if (brandProjects.length === 0 && params.brandId !== brand.id) {
        continue; // 跳过没有项目的品牌（除非特定查询某个品牌）
      }

      const summary = await this.calculateBrandFinancialSummary(brand, brandProjects);
      brandSummaries.push(summary);

      totalOrderAmount += summary.orderAmount;
      totalExecutedAmount += summary.executedAmount;
      totalEstimatedProfit += summary.estimatedProfit;
      totalReceivedAmount += summary.receivedAmount;
      totalPaidAmount += summary.paidProjectAmount;
    }

    const overallProfitMargin = totalOrderAmount > 0 ? (totalEstimatedProfit / totalOrderAmount) * 100 : 0;

    return {
      summary: {
        totalBrands: brandSummaries.length,
        totalOrderAmount,
        totalExecutedAmount,
        totalEstimatedProfit,
        totalReceivedAmount,
        totalPaidAmount,
        overallProfitMargin: Math.round(overallProfitMargin * 100) / 100
      },
      brands: brandSummaries,
      generatedAt: new Date(),
      reportPeriod: {
        startDate: params.startDate ? new Date(params.startDate) : undefined,
        endDate: params.endDate ? new Date(params.endDate) : undefined
      }
    };
  }

  // 获取品牌财务详细报表
  async getBrandFinancialDetail(brandId: string, params: FinancialReportQueryParams = {}): Promise<BrandFinancialDetail> {
    if (this.useDatabase) {
      return await db.getBrandFinancialDetail(brandId, params);
    }

    // 内存模式实现
    const brand = this.brands.get(brandId);
    if (!brand) {
      throw new Error('品牌不存在');
    }

    let brandProjects = Array.from(this.projects.values()).filter(p => p.brandId === brandId);

    // 应用过滤器
    if (params.startDate) {
      const startDate = new Date(params.startDate);
      brandProjects = brandProjects.filter(p => p.period.startDate >= startDate);
    }
    if (params.endDate) {
      const endDate = new Date(params.endDate);
      brandProjects = brandProjects.filter(p => p.period.endDate <= endDate);
    }
    if (params.projectStatus) {
      brandProjects = brandProjects.filter(p => params.projectStatus!.includes(p.status as ProjectStatus));
    }

    const summary = await this.calculateBrandFinancialSummary(brand, brandProjects);

    // 构建详细项目信息
    const projectDetails = await Promise.all(brandProjects.map(async (project) => {
      const totalBudget = project.budget.planningBudget + project.budget.influencerBudget +
                         project.budget.adBudget + project.budget.otherBudget;
      const totalCost = project.cost.influencerCost + project.cost.adCost + project.cost.otherCost;

      // 获取用户信息
      let executorPMInfo;
      try {
        const userInfo = await this.getUserInfo(project.executorPM);
        if (userInfo) {
          executorPMInfo = {
            userid: userInfo.userid,
            name: userInfo.name,
            department: userInfo.department || ''
          };
        }
      } catch (error) {
        console.warn('获取用户信息失败:', error);
      }

      return {
        id: project.id,
        projectName: project.projectName,
        status: project.status as ProjectStatus,
        documentType: project.documentType,
        contractType: project.contractType,
        period: project.period,
        budget: {
          planningBudget: project.budget.planningBudget,
          totalBudget
        },
        cost: {
          totalCost,
          estimatedInfluencerRebate: project.cost.estimatedInfluencerRebate
        },
        profit: project.profit,
        revenue: {
          plannedAmount: 0, // 需要从收入表获取
          receivedAmount: 0, // 需要从收入表获取
          unreceivedAmount: 0
        },
        weeklyBudgets: {
          totalContractAmount: 0, // 需要从周预算表获取
          paidAmount: 0, // 需要从周预算表获取
          unpaidAmount: 0
        },
        executorPM: project.executorPM,
        executorPMInfo
      };
    }));

    return {
      brandInfo: {
        id: brand.id,
        name: brand.name,
        description: brand.description,
        logo: brand.logo
      },
      summary,
      projects: projectDetails,
      revenueAnalysis: {
        totalPlannedRevenue: 0,
        totalReceivedRevenue: 0,
        revenueByStatus: [],
        monthlyTrend: []
      },
      costAnalysis: {
        totalWeeklyBudgets: 0,
        totalPaidAmount: 0,
        totalUnpaidAmount: 0,
        budgetsByServiceType: []
      }
    };
  }

  // 计算品牌财务汇总
  private async calculateBrandFinancialSummary(brand: Brand, projects: Project[]): Promise<BrandFinancialSummary> {
    let orderAmount = 0;
    let executedAmount = 0;
    let executingAmount = 0;
    let estimatedProfit = 0;
    let receivedAmount = 0;
    let unreceivedAmount = 0;
    let paidProjectAmount = 0;
    let unpaidProjectAmount = 0;

    let activeProjectCount = 0;
    let completedProjectCount = 0;

    for (const project of projects) {
      // 品牌下单金额 = 项目规划预算总和
      orderAmount += project.budget.planningBudget;

      // 预估毛利 = 项目利润总和
      estimatedProfit += project.profit.profit;

      // 根据项目状态计算已执行和执行中金额
      if (project.status === 'completed') {
        completedProjectCount++;
        // 已执行金额 = 已完成项目的实际支出（成本）
        const totalCost = project.cost.influencerCost + project.cost.adCost + project.cost.otherCost;
        executedAmount += totalCost;
      } else if (project.status === 'active') {
        activeProjectCount++;
        // 执行中项目金额 = 进行中项目的预算
        executingAmount += project.budget.planningBudget;
      } else if (project.status === 'draft') {
        // 草稿状态的项目不计入执行金额
      } else if (project.status === 'cancelled') {
        // 已取消的项目不计入执行金额
      }

      // 已回款和未回款
      if (project.revenues && project.revenues.length > 0) {
        for (const revenue of project.revenues) {
          if (revenue.status === 'received') {
            receivedAmount += revenue.actualAmount || revenue.plannedAmount;
          } else {
            unreceivedAmount += revenue.plannedAmount;
          }
        }
      }

      // 已支付和未支付
      if (project.weeklyBudgets && project.weeklyBudgets.length > 0) {
        for (const budget of project.weeklyBudgets) {
          paidProjectAmount += budget.paidAmount;
          unpaidProjectAmount += budget.contractAmount - budget.paidAmount;
        }
      }

    }

    // 计算预估毛利率
    const estimatedProfitMargin = orderAmount > 0 ? (estimatedProfit / orderAmount) * 100 : 0;

    return {
      brandId: brand.id,
      brandName: brand.name,
      orderAmount: Math.round(orderAmount * 100) / 100,
      executedAmount: Math.round(executedAmount * 100) / 100,
      executingAmount: Math.round(executingAmount * 100) / 100,
      estimatedProfit: Math.round(estimatedProfit * 100) / 100,
      estimatedProfitMargin: Math.round(estimatedProfitMargin * 100) / 100,
      receivedAmount: Math.round(receivedAmount * 100) / 100,
      unreceivedAmount: Math.round(unreceivedAmount * 100) / 100,
      paidProjectAmount: Math.round(paidProjectAmount * 100) / 100,
      unpaidProjectAmount: Math.round(unpaidProjectAmount * 100) / 100,
      projectCount: projects.length,
      activeProjectCount,
      completedProjectCount
    };
  }

  // 通知相关方法

  /**
   * 发送项目创建通知给执行PM
   */
  private async sendProjectCreatedNotification(project: Project, createdBy: string): Promise<void> {
    try {
      // 获取执行PM用户信息
      const executorPMInfo = await this.userSyncService.getUserInfo(project.executorPM);
      if (!executorPMInfo) {
        console.warn(`无法获取执行PM用户信息: ${project.executorPM}`);
        return;
      }

      // 获取创建人用户信息
      const creatorInfo = await this.userSyncService.getUserInfo(createdBy);
      const creatorName = creatorInfo?.name || createdBy;

      // 获取品牌信息
      const brand = await this.getBrand(project.brandId);
      const brandName = brand?.name || '未知品牌';

      // 构建通知数据
      const defaultDate = new Date().toISOString().split('T')[0];
      const startDate = project.period?.startDate
        ? (project.period.startDate instanceof Date
          ? project.period.startDate.toISOString().split('T')[0]
          : new Date(project.period.startDate).toISOString().split('T')[0])
        : defaultDate;

      const endDate = project.period?.endDate
        ? (project.period.endDate instanceof Date
          ? project.period.endDate.toISOString().split('T')[0]
          : new Date(project.period.endDate).toISOString().split('T')[0])
        : defaultDate;

      const notificationData = {
        projectName: project.projectName,
        brandName,
        executorPMName: executorPMInfo.name,
        budget: project.budget.planningBudget,
        startDate: startDate as string,
        endDate: endDate as string,
        creatorName
      };

      // 发送通知
      const success = await this.notificationService.sendProjectCreatedNotification(
        project.executorPM,
        notificationData
      );

      if (success) {
        console.log(`项目创建通知发送成功: ${project.projectName} -> ${executorPMInfo.name}`);
      } else {
        console.error(`项目创建通知发送失败: ${project.projectName} -> ${executorPMInfo.name}`);
      }
    } catch (error) {
      console.error('发送项目创建通知时出错:', error);
    }
  }

  /**
   * 检查周预算是否超过项目成本10%并发送通知
   */
  async checkWeeklyBudgetExceeded(weeklyBudget: WeeklyBudget, projectId: string): Promise<void> {
    try {
      // 获取项目信息
      const project = await this.getProject(projectId);
      if (!project) {
        console.warn(`无法获取项目信息: ${projectId}`);
        return;
      }

      // 计算项目总成本
      const totalProjectCost = project.cost.influencerCost + project.cost.adCost + project.cost.otherCost;

      // 计算超额比例
      const exceedPercentage = (weeklyBudget.contractAmount / totalProjectCost) * 100;

      // 如果超过10%，发送通知
      if (exceedPercentage > 10) {
        // 获取项目创建人用户信息
        const creatorInfo = await this.userSyncService.getUserInfo(project.createdBy);
        if (!creatorInfo) {
          console.warn(`无法获取项目创建人用户信息: ${project.createdBy}`);
          return;
        }

        // 获取品牌信息
        const brand = await this.getBrand(project.brandId);
        const brandName = brand?.name || '未知品牌';

        // 构建通知数据
        const notificationData = {
          projectName: project.projectName,
          brandName,
          weeklyBudgetTitle: weeklyBudget.title,
          contractAmount: weeklyBudget.contractAmount,
          projectCost: totalProjectCost,
          exceedPercentage,
          creatorName: creatorInfo.name
        };

        // 发送通知
        const success = await this.notificationService.sendWeeklyBudgetExceededNotification(
          project.createdBy,
          notificationData
        );

        if (success) {
          console.log(`周预算超额通知发送成功: ${weeklyBudget.title} -> ${creatorInfo.name}`);
        } else {
          console.error(`周预算超额通知发送失败: ${weeklyBudget.title} -> ${creatorInfo.name}`);
        }
      }
    } catch (error) {
      console.error('检查周预算超额时出错:', error);
    }
  }
}
