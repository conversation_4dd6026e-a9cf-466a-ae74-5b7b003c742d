# 钉钉微H5应用后端API文档

## 概述

本文档描述了钉钉微H5应用后端的所有API接口，包括认证、项目管理、品牌管理、收入管理、供应商管理、周预算管理、审批管理等功能。

### 🆕 新增功能：钉钉对公付款审批

系统已集成完整的钉钉对公付款审批功能，支持：
- **12字段审批表单**：申请人、申请部门、关联审批单、所属项目、付款事由、合同签署主体、付款总额、期望付款时间、付款方式、收款账号、发票文件、附件、备注
- **文件上传管理**：支持发票和附件文件上传，自动验证文件类型和大小
- **用户认证集成**：自动获取当前用户信息，无需手动填写申请人信息
- **审批状态同步**：实时同步钉钉审批状态，审批通过后自动更新周预算已付金额
- **完整的审批流程**：从发起审批到状态跟踪的全流程管理

## 基础信息

- **基础URL**: `http://localhost:3000/api`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 功能模块

1. [认证管理](#认证管理) - 钉钉登录认证
2. [项目管理](#项目管理) - 项目CRUD操作
3. [品牌管理](#品牌管理) - 品牌信息管理
4. [收入管理](#收入管理) - 项目收入预测管理
5. [供应商管理](#供应商管理) - 供应商信息管理
6. [周预算管理](#周预算管理) - 项目周预算管理
7. [审批管理](#审批管理) - 钉钉对公付款审批流程
8. [文件管理](#文件管理) - 文件上传下载

## 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

错误响应格式：
```json
{
  "success": false,
  "message": "错误信息",
  "errors": []
}
```

## 数据类型说明

### 枚举类型

#### 服务类型 (ServiceType)
- `influencer` - 达人服务
- `advertising` - 投流服务  
- `other` - 其他服务

#### 税率类型 (TaxRate)
- `special_1` - 专票1%
- `special_3` - 专票3%
- `special_6` - 专票6%
- `general` - 普票

#### 供应商状态 (SupplierStatus)
- `active` - 活跃
- `inactive` - 停用
- `pending` - 待审核
- `blacklisted` - 黑名单

#### 周预算状态 (WeeklyBudgetStatus)
- `draft` - 草稿
- `approved` - 已批准
- `executing` - 执行中
- `completed` - 已完成
- `cancelled` - 已取消

#### 收入状态 (RevenueStatus)
- `receiving` - 收款中
- `received` - 已收款
- `cancelled` - 已取消

#### 收入类型 (RevenueType)
- `influencer_income` - 达人收入
- `project_income` - 项目收入
- `other` - 其他收入

#### 审批状态 (ApprovalStatus)
- `none` - 无审批
- `pending` - 审批中
- `approved` - 已通过
- `rejected` - 已拒绝
- `cancelled` - 已撤销

#### 合同签署主体 (ContractEntity)
- `company_a` - 公司A
- `company_b` - 公司B
- `subsidiary` - 子公司
- `other` - 其他

#### 付款方式 (PaymentMethod)
- `bank_transfer` - 银行转账
- `online_payment` - 网银支付
- `check` - 支票
- `cash` - 现金
- `other` - 其他

---

## 认证管理

### 钉钉登录

**接口地址**: `POST /auth/dingtalk`

**请求参数**:
```json
{
  "authCode": "string",
  "corpId": "string"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_string",
    "user": {
      "userid": "user123",
      "name": "张三",
      "avatar": "https://example.com/avatar.jpg"
    }
  },
  "message": "登录成功"
}
```

---

## 项目管理

### 创建项目

**接口地址**: `POST /projects`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "documentType": "project_initiation",
  "brandId": "brand_id",
  "projectName": "项目名称",
  "period": {
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
  },
  "budget": {
    "planningBudget": 1000000,
    "influencerBudget": 400000,
    "adBudget": 300000,
    "otherBudget": 100000
  },
  "cost": {
    "influencerCost": 350000,
    "adCost": 280000,
    "otherCost": 80000,
    "estimatedInfluencerRebate": 20000
  },
  "executorPM": "user_id",
  "contentMediaIds": ["user_id1", "user_id2"],
  "contractType": "annual_frame",
  "settlementRules": "结算规则",
  "kpi": "KPI指标",
  "attachmentIds": ["attachment_id1", "attachment_id2"]
}
```

**字段说明**:
- `attachmentIds` (array, optional): 附件ID数组，通过 `/projects/upload` 接口上传文件后获得的附件ID

### 获取项目列表

**接口地址**: `GET /projects`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page` (number, optional): 页码，默认1
- `pageSize` (number, optional): 每页数量，默认20
- `status` (string, optional): 项目状态
- `brandId` (string, optional): 品牌ID
- `documentType` (string, optional): 文档类型
- `keyword` (string, optional): 关键词搜索

**响应示例**:
```json
{
  "success": true,
  "data": {
    "projects": [],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  },
  "message": "获取项目列表成功"
}
```

### 获取单个项目

**接口地址**: `GET /projects/{id}`

**请求头**: `Authorization: Bearer {token}`

### 更新项目

**接口地址**: `PUT /projects/{id}`

**请求头**: `Authorization: Bearer {token}`

### 删除项目

**接口地址**: `DELETE /projects/{id}`

**请求头**: `Authorization: Bearer {token}`

---

## 品牌管理

### 创建品牌

**接口地址**: `POST /brands`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "name": "品牌名称",
  "description": "品牌描述",
  "logo": "logo_url"
}
```

### 获取品牌列表

**接口地址**: `GET /brands`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page` (number, optional): 页码
- `pageSize` (number, optional): 每页数量
- `status` (string, optional): 品牌状态
- `keyword` (string, optional): 关键词搜索

### 获取单个品牌

**接口地址**: `GET /brands/{id}`

### 更新品牌

**接口地址**: `PUT /brands/{id}`

### 删除品牌

**接口地址**: `DELETE /brands/{id}`

---

## 收入管理

### 创建项目收入

**接口地址**: `POST /projects/{projectId}/revenues`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "title": "收入标题",
  "revenueType": "milestone",
  "plannedAmount": 500000,
  "plannedDate": "2024-06-30",
  "milestone": "里程碑描述",
  "paymentTerms": "付款条件",
  "notes": "备注"
}
```

### 获取收入列表

**接口地址**: `GET /revenues`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page` (number, optional): 页码
- `pageSize` (number, optional): 每页数量
- `projectId` (string, optional): 项目ID
- `status` (string, optional): 收入状态
- `revenueType` (string, optional): 收入类型
- `startDate` (string, optional): 开始日期
- `endDate` (string, optional): 结束日期

### 更新收入

**接口地址**: `PUT /revenues/{id}`

**请求参数**:
```json
{
  "id": "revenue_id",
  "status": "confirmed",
  "actualAmount": 500000,
  "confirmedDate": "2024-06-25",
  "notes": "收入已确认"
}
```

### 删除收入

**接口地址**: `DELETE /revenues/{id}`

### 获取收入统计

**接口地址**: `GET /revenues/stats`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalPlannedRevenue": 2000000,
    "totalActualRevenue": 1500000,
    "totalInvoicedRevenue": 1200000,
    "totalReceivedRevenue": 1000000,
    "revenueByStatus": [
      {
        "status": "planned",
        "count": 5,
        "totalAmount": 500000
      }
    ],
    "revenueByType": [
      {
        "type": "milestone",
        "count": 3,
        "totalAmount": 800000
      }
    ],
    "monthlyRevenueTrend": [
      {
        "month": "2024-01",
        "plannedAmount": 200000,
        "actualAmount": 180000
      }
    ]
  },
  "message": "获取收入统计成功"
}
```

---

## 供应商管理

### 创建供应商

**接口地址**: `POST /suppliers`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "name": "供应商名称",
  "shortName": "简称",
  "code": "SUP001",
  "contactPerson": "联系人",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>",
  "address": "供应商地址",
  "taxNumber": "税号",
  "bankAccount": "银行账号",
  "bankName": "开户银行",
  "legalPerson": "法人代表",
  "serviceTypes": ["influencer", "advertising"],
  "preferredTaxRate": "special_6",
  "creditLimit": 1000000,
  "paymentTerms": "付款条件",
  "rating": 5,
  "notes": "备注信息"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "supplier_id",
    "name": "供应商名称",
    "shortName": "简称",
    "code": "SUP001",
    "contactPerson": "联系人",
    "contactPhone": "***********",
    "contactEmail": "<EMAIL>",
    "address": "供应商地址",
    "taxNumber": "税号",
    "bankAccount": "银行账号",
    "bankName": "开户银行",
    "legalPerson": "法人代表",
    "serviceTypes": ["influencer", "advertising"],
    "preferredTaxRate": "special_6",
    "creditLimit": 1000000,
    "paymentTerms": "付款条件",
    "status": "active",
    "rating": 5,
    "notes": "备注信息",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "createdBy": "user_id",
    "updatedBy": "user_id"
  },
  "message": "创建供应商成功"
}
```

### 获取供应商列表

**接口地址**: `GET /suppliers`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page` (number, optional): 页码，默认1
- `pageSize` (number, optional): 每页数量，默认20
- `status` (string, optional): 供应商状态 (active/inactive/pending/blacklisted)
- `serviceType` (string, optional): 服务类型 (influencer/advertising/other)
- `keyword` (string, optional): 关键词搜索（名称、联系人、联系方式）
- `sortBy` (string, optional): 排序字段 (name/createdAt/rating)
- `sortOrder` (string, optional): 排序方向 (asc/desc)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "suppliers": [
      {
        "id": "supplier_id",
        "name": "供应商名称",
        "shortName": "简称",
        "serviceTypes": ["influencer", "advertising"],
        "status": "active",
        "rating": 5,
        "contactPerson": "联系人",
        "contactPhone": "***********",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "totalPages": 3
  },
  "message": "获取供应商列表成功"
}
```

### 获取单个供应商

**接口地址**: `GET /suppliers/{id}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "supplier_id",
    "name": "供应商名称",
    "shortName": "简称",
    "code": "SUP001",
    "contactPerson": "联系人",
    "contactPhone": "***********",
    "contactEmail": "<EMAIL>",
    "address": "供应商地址",
    "taxNumber": "税号",
    "bankAccount": "银行账号",
    "bankName": "开户银行",
    "legalPerson": "法人代表",
    "serviceTypes": ["influencer", "advertising"],
    "preferredTaxRate": "special_6",
    "creditLimit": 1000000,
    "paymentTerms": "付款条件",
    "status": "active",
    "rating": 5,
    "notes": "备注信息",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "createdBy": "user_id",
    "updatedBy": "user_id"
  },
  "message": "获取供应商成功"
}
```

### 更新供应商

**接口地址**: `PUT /suppliers/{id}`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "name": "更新后的供应商名称",
  "status": "active",
  "rating": 4,
  "notes": "更新后的备注",
  "serviceTypes": ["influencer", "advertising", "other"],
  "creditLimit": 1200000
}
```

### 删除供应商

**接口地址**: `DELETE /suppliers/{id}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "删除供应商成功"
}
```

### 获取供应商统计

**接口地址**: `GET /suppliers/stats`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalSuppliers": 50,
    "activeSuppliers": 45,
    "suppliersByServiceType": [
      {
        "serviceType": "influencer",
        "count": 30
      },
      {
        "serviceType": "advertising",
        "count": 25
      },
      {
        "serviceType": "other",
        "count": 10
      }
    ],
    "suppliersByRating": [
      {
        "rating": 5,
        "count": 20
      },
      {
        "rating": 4,
        "count": 15
      }
    ]
  },
  "message": "获取供应商统计成功"
}
```

## 周预算管理

### 创建项目周预算

**接口地址**: `POST /projects/{projectId}/weekly-budgets`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "title": "第1周达人投放预算",
  "weekStartDate": "2024-01-01",
  "weekEndDate": "2024-01-07",
  "serviceType": "influencer",
  "serviceContent": "小红书达人投放，包含图文和视频内容",
  "remarks": "重点关注美妆类达人",
  "contractAmount": 50000,
  "taxRate": "special_6",
  "supplierId": "supplier_id"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "weekly_budget_id",
    "title": "第1周达人投放预算",
    "weekStartDate": "2024-01-01",
    "weekEndDate": "2024-01-07",
    "weekNumber": 1,
    "year": 2024,
    "serviceType": "influencer",
    "serviceContent": "小红书达人投放，包含图文和视频内容",
    "remarks": "重点关注美妆类达人",
    "contractAmount": 50000,
    "taxRate": "special_6",
    "paidAmount": 0,
    "remainingAmount": 50000,
    "status": "draft",
    "projectId": "project_id",
    "supplierId": "supplier_id",
    "supplier": {
      "id": "supplier_id",
      "name": "供应商名称",
      "shortName": "简称"
    },
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "createdBy": "user_id",
    "updatedBy": "user_id"
  },
  "message": "创建周预算成功"
}
```

### 批量创建项目周预算

**接口地址**: `POST /projects/{projectId}/weekly-budgets/batch`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "startDate": "2024-01-08",
  "endDate": "2024-01-28",
  "serviceType": "advertising",
  "defaultContractAmount": 30000,
  "defaultTaxRate": "special_3"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "weekly_budget_id_1",
      "title": "第1周预算",
      "weekStartDate": "2024-01-08",
      "weekEndDate": "2024-01-14",
      "serviceType": "advertising",
      "contractAmount": 30000,
      "status": "draft"
    },
    {
      "id": "weekly_budget_id_2",
      "title": "第2周预算",
      "weekStartDate": "2024-01-15",
      "weekEndDate": "2024-01-21",
      "serviceType": "advertising",
      "contractAmount": 30000,
      "status": "draft"
    },
    {
      "id": "weekly_budget_id_3",
      "title": "第3周预算",
      "weekStartDate": "2024-01-22",
      "weekEndDate": "2024-01-28",
      "serviceType": "advertising",
      "contractAmount": 30000,
      "status": "draft"
    }
  ],
  "message": "批量创建周预算成功"
}
```

### 获取周预算列表

**接口地址**: `GET /weekly-budgets`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page` (number, optional): 页码，默认1
- `pageSize` (number, optional): 每页数量，默认20
- `projectId` (string, optional): 项目ID
- `supplierId` (string, optional): 供应商ID
- `serviceType` (string, optional): 服务类型 (influencer/advertising/other)
- `status` (string, optional): 预算状态 (draft/approved/executing/completed/cancelled)
- `approvalStatus` (string, optional): 审批状态 (none/pending/approved/rejected/cancelled)
- `year` (number, optional): 年份
- `weekNumber` (number, optional): 周数
- `startDate` (string, optional): 开始日期 (YYYY-MM-DD)
- `endDate` (string, optional): 结束日期 (YYYY-MM-DD)
- `sortBy` (string, optional): 排序字段 (weekStartDate/contractAmount/createdAt)
- `sortOrder` (string, optional): 排序方向 (asc/desc)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "weeklyBudgets": [
      {
        "id": "weekly_budget_id",
        "title": "第1周达人投放预算",
        "weekStartDate": "2024-01-01",
        "weekEndDate": "2024-01-07",
        "weekNumber": 1,
        "year": 2024,
        "serviceType": "influencer",
        "contractAmount": 50000,
        "paidAmount": 25000,
        "remainingAmount": 25000,
        "status": "approved",
        "approvalStatus": "approved",
        "approvalAmount": 25000,
        "approvalReason": "第一期付款",
        "projectId": "project_id",
        "supplierId": "supplier_id",
        "supplier": {
          "id": "supplier_id",
          "name": "供应商名称",
          "shortName": "简称"
        },
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 20,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "message": "获取周预算列表成功"
}
```

### 获取单个周预算

**接口地址**: `GET /weekly-budgets/{id}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "weekly_budget_id",
    "title": "第1周达人投放预算",
    "weekStartDate": "2024-01-01",
    "weekEndDate": "2024-01-07",
    "weekNumber": 1,
    "year": 2024,
    "serviceType": "influencer",
    "serviceContent": "小红书达人投放，包含图文和视频内容",
    "remarks": "重点关注美妆类达人",
    "contractAmount": 50000,
    "taxRate": "special_6",
    "paidAmount": 25000,
    "remainingAmount": 25000,
    "status": "approved",
    "approvalStatus": "approved",
    "approvalAmount": 25000,
    "approvalReason": "第一期付款",
    "projectId": "project_id",
    "supplierId": "supplier_id",
    "supplier": {
      "id": "supplier_id",
      "name": "供应商名称",
      "shortName": "简称",
      "contactPerson": "联系人",
      "contactPhone": "***********"
    },
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "createdBy": "user_id",
    "updatedBy": "user_id"
  },
  "message": "获取周预算成功"
}
```

### 更新周预算

**接口地址**: `PUT /weekly-budgets/{id}`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "id": "weekly_budget_id",
  "status": "approved",
  "paidAmount": 25000,
  "remarks": "已支付50%预付款",
  "serviceContent": "更新后的服务内容"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "weekly_budget_id",
    "title": "第1周达人投放预算",
    "weekStartDate": "2024-01-01",
    "weekEndDate": "2024-01-07",
    "serviceType": "influencer",
    "contractAmount": 50000,
    "paidAmount": 25000,
    "remainingAmount": 25000,
    "status": "approved",
    "remarks": "已支付50%预付款",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  },
  "message": "更新周预算成功"
}
```

### 删除周预算

**接口地址**: `DELETE /weekly-budgets/{id}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "删除周预算成功"
}
```

### 获取周预算统计

**接口地址**: `GET /weekly-budgets/stats`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalBudgets": 50,
    "totalContractAmount": 2500000,
    "totalPaidAmount": 1200000,
    "totalRemainingAmount": 1300000,
    "budgetsByServiceType": [
      {
        "serviceType": "influencer",
        "count": 20,
        "totalAmount": 1000000
      },
      {
        "serviceType": "advertising",
        "count": 25,
        "totalAmount": 1200000
      },
      {
        "serviceType": "other",
        "count": 5,
        "totalAmount": 300000
      }
    ],
    "budgetsByStatus": [
      {
        "status": "draft",
        "count": 10,
        "totalAmount": 500000
      },
      {
        "status": "approved",
        "count": 20,
        "totalAmount": 1000000
      },
      {
        "status": "executing",
        "count": 15,
        "totalAmount": 750000
      },
      {
        "status": "completed",
        "count": 5,
        "totalAmount": 250000
      }
    ],
    "budgetsBySupplier": [
      {
        "supplierId": "supplier_id_1",
        "supplierName": "供应商A",
        "count": 15,
        "totalAmount": 750000
      },
      {
        "supplierId": "supplier_id_2",
        "supplierName": "供应商B",
        "count": 10,
        "totalAmount": 500000
      }
    ],
    "weeklyTrend": [
      {
        "week": "2024-W01",
        "contractAmount": 100000,
        "paidAmount": 50000
      },
      {
        "week": "2024-W02",
        "contractAmount": 120000,
        "paidAmount": 60000
      }
    ]
  },
  "message": "获取周预算统计成功"
}
```

---

## 审批管理

### 发起对公付款审批

**接口地址**: `POST /weekly-budgets/approval`

**请求头**:
- `Authorization: Bearer {token}`
- `Content-Type: application/json`

**请求参数**:
```json
{
  "weeklyBudgetId": "string",           // 周预算ID (必填)
  "totalAmount": 10000,                 // 付款总额 (必填)
  "paymentReason": "string",            // 付款事由 (必填)
  "contractEntity": "company_a",        // 合同签署主体 (可选)
  "expectedPaymentDate": "2024-01-15",  // 期望付款时间 (必填)
  "paymentMethod": "bank_transfer",     // 付款方式 (可选)
  "receivingAccount": {                 // 收款账号信息 (必填)
    "accountName": "string",            // 账户名称 (必填)
    "accountNumber": "string",          // 账号 (必填)
    "bankName": "string",               // 开户银行 (必填)
    "bankCode": "string"                // 银行代码 (可选)
  },
  "relatedApprovalId": "string",        // 关联审批单 (可选)
  "invoiceFiles": ["string"],           // 发票文件URL列表 (可选)
  "attachments": ["string"],            // 附件URL列表 (可选)
  "remark": "string"                    // 备注 (可选)
}
```

**字段说明**:
- `contractEntity`: 枚举值 `company_a | company_b | subsidiary | other`
- `paymentMethod`: 枚举值 `bank_transfer | online_payment | check | cash | other`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "approval_instance_id",
    "processInstanceId": "dingtalk_process_id",
    "title": "项目 - 周预算标题 - 对公付款申请",
    "status": "PENDING",
    "approvalAmount": 10000,
    "weeklyBudgetId": "budget_id",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "发起审批成功"
}
```

### 获取审批实例列表

**接口地址**: `GET /approvals`

**请求头**:
- `Authorization: Bearer {token}`

**查询参数**:
- `page` (number, optional): 页码，默认1
- `pageSize` (number, optional): 每页数量，默认20
- `weeklyBudgetId` (string, optional): 周预算ID
- `status` (string, optional): 审批状态 `NONE | PENDING | APPROVED | REJECTED | CANCELLED`
- `originatorUserId` (string, optional): 发起人用户ID
- `startDate` (string, optional): 开始日期 YYYY-MM-DD
- `endDate` (string, optional): 结束日期 YYYY-MM-DD
- `sortBy` (string, optional): 排序字段
- `sortOrder` (string, optional): 排序方向 `asc | desc`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "approvals": [
      {
        "id": "approval_id",
        "processInstanceId": "dingtalk_process_id",
        "title": "审批标题",
        "status": "PENDING",
        "approvalAmount": 10000,
        "createTime": "2024-01-01T00:00:00.000Z",
        "weeklyBudget": {
          "id": "budget_id",
          "title": "周预算标题"
        }
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  },
  "message": "获取审批列表成功"
}
```

### 获取单个审批实例

**接口地址**: `GET /approvals/{id}`

**请求头**:
- `Authorization: Bearer {token}`

**路径参数**:
- `id` (string): 审批实例ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "approval_id",
    "processInstanceId": "dingtalk_process_id",
    "processCode": "PROC-PAYMENT-001",
    "title": "审批标题",
    "originatorUserId": "user_id",
    "status": "PENDING",
    "result": null,
    "createTime": "2024-01-01T00:00:00.000Z",
    "finishTime": null,
    "approvalAmount": 10000,
    "reason": "付款原因",
    "remark": "备注信息",
    "weeklyBudget": {
      "id": "budget_id",
      "title": "周预算标题",
      "project": {
        "id": "project_id",
        "name": "项目名称"
      }
    }
  },
  "message": "获取审批详情成功"
}
```

### 同步审批状态

**接口地址**: `POST /approvals/sync/{processInstanceId}`

**请求头**:
- `Authorization: Bearer {token}`

**路径参数**:
- `processInstanceId` (string): 钉钉审批实例ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "approval_id",
    "status": "APPROVED",
    "result": "agree",
    "finishTime": "2024-01-01T12:00:00.000Z"
  },
  "message": "同步审批状态成功"
}
```

### 获取审批统计信息

**接口地址**: `GET /approvals/stats`

**请求头**:
- `Authorization: Bearer {token}`

**查询参数**:
- `startDate` (string, optional): 开始日期 YYYY-MM-DD
- `endDate` (string, optional): 结束日期 YYYY-MM-DD

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total": 100,
    "pending": 15,
    "approved": 70,
    "rejected": 10,
    "cancelled": 5,
    "totalAmount": 1000000,
    "approvedAmount": 700000,
    "pendingAmount": 150000
  },
  "message": "获取审批统计成功"
}
```

### 批量同步审批状态

**接口地址**: `POST /approvals/batch-sync`

**请求头**:
- `Authorization: Bearer {token}`
- `Content-Type: application/json`

**请求参数**:
```json
{
  "processInstanceIds": ["process_id_1", "process_id_2"]
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total": 2,
    "success": 2,
    "failure": 0,
    "results": [
      {
        "processInstanceId": "process_id_1",
        "status": "fulfilled",
        "data": { "status": "APPROVED" }
      },
      {
        "processInstanceId": "process_id_2",
        "status": "fulfilled",
        "data": { "status": "PENDING" }
      }
    ]
  },
  "message": "批量同步完成，成功2个，失败0个"
}
```

### 审批回调接口

**接口地址**: `POST /approvals/callback`

**说明**: 此接口用于接收钉钉审批状态变更回调，无需认证

**请求参数**:
```json
{
  "EventType": "bpms_instance_change",
  "processInstanceId": "process_instance_id",
  "processCode": "PROC-PAYMENT-001",
  "corpId": "corp_id",
  "createTime": 1640995200000,
  "title": "对公付款申请",
  "type": "finish",
  "result": "agree",
  "staffId": "user_id"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "处理审批回调成功"
}
```

---

## 文件管理

### 上传审批文件

**接口地址**: `POST /approvals/upload/{type}`

**请求头**:
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**路径参数**:
- `type` (string): 文件类型 `invoice | attachment`

**请求参数**:
- `file` (file): 要上传的文件

**文件限制**:
- **发票文件**: 支持 pdf, jpg, jpeg, png, doc, docx, xls, xlsx，最大10MB
- **附件文件**: 支持 pdf, doc, docx, xls, xlsx, ppt, pptx, txt, jpg, jpeg, png, zip, rar，最大20MB

**响应示例**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "filename": "invoice_001.pdf",
        "url": "/uploads/invoice/1705123456789_invoice_001.pdf",
        "size": 1024000
      }
    ]
  },
  "message": "发票文件上传成功"
}
```

### 上传项目文件

**接口地址**: `POST /projects/upload`

**请求头**:
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**查询参数**:
- `projectId` (string, optional): 项目ID，如果提供则直接关联到项目

**请求参数**:
- `file` (file): 要上传的文件

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "attachment_id",
    "filename": "1234567890_document.pdf",
    "originalName": "项目文档.pdf",
    "mimeType": "application/pdf",
    "size": 1024000,
    "url": "/uploads/1234567890_document.pdf",
    "uploadedAt": "2024-01-01T12:00:00Z",
    "uploadedBy": "user123"
  },
  "message": "文件上传成功"
}
```

**使用说明**:
1. 如果提供了 `projectId` 查询参数，文件会直接关联到指定项目
2. 如果没有提供 `projectId`，文件会被上传但不关联项目，可以在创建/更新项目时通过 `attachmentIds` 字段关联

### 上传审批附件到钉钉

**接口地址**: `POST /approvals/upload/{type}`

**请求头**:
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**路径参数**:
- `type` (string): 文件类型，可选值：`invoice`（发票文件）、`attachment`（附件）

**请求参数**:
- `file` (file): 要上传的文件（支持多文件上传）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "filename": "invoice.pdf",
        "originalName": "发票.pdf",
        "mediaId": "dingtalk_media_id_123",
        "size": 1024000,
        "mimeType": "application/pdf"
      }
    ],
    "mediaIds": ["dingtalk_media_id_123"]
  },
  "message": "附件文件上传到钉钉成功"
}
```

**使用说明**:
1. 文件会被上传到钉钉媒体库，返回钉钉媒体ID
2. 媒体ID可以在发起审批时使用，钉钉会在审批表单中显示附件
3. 支持的文件类型：PDF、图片、Word、Excel等常见格式

### 发起带附件的对公付款审批

**接口地址**: `POST /approvals/payment-with-files`

**请求头**:
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**请求参数**:
- `weeklyBudgetId` (string): 周预算ID
- `totalAmount` (number): 付款总额
- `paymentReason` (string): 付款事由
- `department` (number): 申请部门
- `contractEntity` (string): 合同签署主体
- `expectedPaymentDate` (string): 期望付款时间
- `paymentMethod` (string): 付款方式
- `receivingAccount` (string): 收款账号信息（JSON字符串）
- `remark` (string): 备注
- `file` (file): 附件文件（支持多文件上传）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "processInstanceId": "dingtalk_process_instance_id",
    "approvalId": "approval_id_123",
    "uploadedFiles": 2
  },
  "message": "发起审批成功，已上传2个附件"
}
```

**使用说明**:
1. 支持在发起审批的同时上传附件文件
2. 文件会自动上传到钉钉媒体库并关联到审批表单
3. 审批人可以在钉钉审批界面中查看和下载附件

### 下载文件

**接口地址**: `GET /files/{fileId}`

**请求头**: `Authorization: Bearer {token}`

**响应**: 文件流

---

## 测试接口

> **注意**: 以下测试接口仅在开发环境中可用，无需认证，用于功能测试。

### 测试项目管理

- `POST /test/brands` - 创建品牌（测试用）
- `GET /test/brands` - 获取品牌列表（测试用）
- `POST /test/projects` - 创建项目（测试用）
- `GET /test/projects` - 获取项目列表（测试用）

### 测试收入管理

- `POST /test/projects/{projectId}/revenues` - 创建收入（测试用）
- `GET /test/revenues` - 获取收入列表（测试用）
- `GET /test/revenues/{id}` - 获取单个收入（测试用）
- `PUT /test/revenues/{id}` - 更新收入（测试用）
- `DELETE /test/revenues/{id}` - 删除收入（测试用）
- `GET /test/revenues/stats` - 获取收入统计（测试用）

### 测试供应商管理

- `POST /test/suppliers` - 创建供应商（测试用）
- `GET /test/suppliers` - 获取供应商列表（测试用）
- `GET /test/suppliers/{id}` - 获取单个供应商（测试用）
- `PUT /test/suppliers/{id}` - 更新供应商（测试用）
- `DELETE /test/suppliers/{id}` - 删除供应商（测试用）

### 测试周预算管理

- `POST /test/projects/{projectId}/weekly-budgets` - 创建周预算（测试用）
- `POST /test/projects/{projectId}/weekly-budgets/batch` - 批量创建周预算（测试用）
- `GET /test/weekly-budgets` - 获取周预算列表（测试用）
- `GET /test/weekly-budgets/{id}` - 获取单个周预算（测试用）
- `PUT /test/weekly-budgets/{id}` - 更新周预算（测试用）
- `DELETE /test/weekly-budgets/{id}` - 删除周预算（测试用）
- `GET /test/weekly-budgets/stats` - 获取周预算统计（测试用）

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 常见错误示例

### 参数验证失败
```json
{
  "success": false,
  "message": "参数验证失败",
  "errors": [
    {
      "field": "name",
      "message": "名称不能为空"
    },
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ]
}
```

### 资源不存在
```json
{
  "success": false,
  "message": "项目不存在"
}
```

### 权限不足
```json
{
  "success": false,
  "message": "权限不足，无法访问该资源"
}
```

---

## 更新日志

### v1.2.0 (2024-01-01)
- ✅ 新增供应商管理功能
- ✅ 新增周预算管理功能
- ✅ 支持按周管理项目预算
- ✅ 支持供应商评级和状态管理
- ✅ 支持多种服务类型和税率选择
- ✅ 支持批量创建周预算
- ✅ 新增统计分析接口

### v1.1.0 (2023-12-01)
- ✅ 新增项目收入管理功能
- ✅ 支持收入预测和跟踪
- ✅ 新增收入统计分析
- ✅ 优化项目管理流程

### v1.0.0 (2023-11-01)
- ✅ 基础项目管理功能
- ✅ 品牌管理功能
- ✅ 钉钉登录认证
- ✅ 文件上传下载

---

## 联系方式

如有问题或建议，请联系开发团队。

**API文档版本**: v1.2.0
**最后更新**: 2024-01-01
